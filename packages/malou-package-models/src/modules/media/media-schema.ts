import {
    ApplicationLanguage,
    AspectRatio,
    FileFormat,
    MediaCategory,
    MediaConvertedStatus,
    MediaTagCategory,
    MediaTagSubcategory,
    MediaType,
    toLowerCase,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const mediaJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Media',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        restaurantId: {
            description: 'Most medias are associated with a restaurant but some are not (typically user avatars)',
            type: 'string',
            format: 'objectId',
        },
        userId: {
            anyOf: [
                {
                    type: 'null',
                },
                {
                    type: 'string',
                    format: 'objectId',
                    ref: 'User',
                },
            ],
        },
        title: {
            type: 'string',
            nullable: true,
        },
        category: {
            set: setCategory,
            enum: Object.values(MediaCategory),
        },
        format: {
            enum: Object.values(FileFormat),
        },
        type: {
            set: toLowerCase,
            enum: Object.values(MediaType),
        },
        urls: {
            $ref: '#/definitions/Urls',
        },
        sizes: {
            description: 'sizes in bytes',
            $ref: '#/definitions/Sizes',
        },
        postIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'Post',
            },
        },
        originalMediaId: {
            description: 'If not null, the media will not appear in the gallery.',
            anyOf: [
                {
                    type: 'null',
                },
                {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Media',
                },
            ],
        },
        convertedStatus: {
            enum: [...Object.values(MediaConvertedStatus), null],
            default: null,
            nullable: true,
        },
        socialId: {
            description:
                'For most medias (at least the ones uploaded on the Malou app), this field is set to `_id.toString()`. It is set by a pre-validate hook.',
            type: 'string',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        dimensions: {
            $ref: '#/definitions/Dimensions',
        },
        name: {
            type: 'string',
        },
        thumbnail: {
            type: 'string',
            nullable: true,
            default: null,
        },
        duplicatedFromRestaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        duration: {
            description: `
                Duration in seconds if the media is a video. This field is not set on every video:
                  - it is not set on videos uploaded by the mobile app
                  - it was not set on videos uploaded by the original POST /media route but this parameter
                  has been added recently (although it is computed in the browser)
                  - it is however always set on videos uploaded with the new POST /media/upload-v2 route
                  (computed server-side by AWS MediaConvert)`,
            type: 'number',
            nullable: true,
        },
        resizeMetadata: {
            $ref: '#/definitions/ResizeMetadata',
        },
        aspectRatio: {
            type: 'number',
        },
        transformData: {
            $ref: '#/definitions/TransformData',
        },
        folderId: {
            type: 'string',
            format: 'objectId',
            nullable: true,
            default: null,
        },
        deletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true,
            description:
                'If this field is set, the medium has been deleted from the gallery. This does not mean that the medium is unused and has been deleted from AWS S3! A deleted medium does not appear in the gallery but can be used by some posts for instance.',
        },
        aiDescription: {
            type: 'string',
            nullable: true,
            description: 'An AI generated description of the image',
        },
        aiTags: {
            type: 'array',
            items: {
                $ref: '#/definitions/Tag',
            },
        },
        hasDisplayedText: {
            type: 'boolean',
            nullable: true,
            description: 'An AI generated field that is true if the image has text in it (typographic type)',
        },
        storedObjects: {
            type: 'object',
            additionalProperties: false,
            properties: {
                original: {
                    $ref: '#/definitions/StoredObject',
                    description: 'DO NOT USE, only kept for debugging',
                },
                normalized: {
                    $ref: '#/definitions/StoredObject',
                    description: `
                        Normalized media after transforming the original media with our rules.
                        This will be one used when posting.
                        Prefer to use thumbnails instead of this field's url in the Frontend`,
                },
                thumbnail1024Outside: {
                    $ref: '#/definitions/StoredObject',
                    description:
                        'Thumbnail to be used in the Frontend for fast loading. Outside means the height/width are >= 1024 px (https://sharp.pixelplumbing.com/api-resize)',
                },
                thumbnail256Outside: {
                    $ref: '#/definitions/StoredObject',
                    description:
                        'Thumbnail to be used in the Frontend for fast loading. Outside means height/width are >= 256 px (https://sharp.pixelplumbing.com/api-resize)',
                },
            },
            required: ['original', 'normalized', 'thumbnail1024Outside', 'thumbnail256Outside'],
            title: 'Tag',
        },
        isV2: {
            description: `
                Indicate if the media uploaded with the v2 endpoint and so,
                if it contains v2 fields like storedObjects, timelinePreviewFrames256h, isVideoNormalized, ...
            `,
            type: 'boolean',
        },
        timelinePreviewFrames256h: {
            type: 'array',
            items: { $ref: '#/definitions/StoredObject' },
            description: `
                List of frames to display a timeline of the video, separated by a regular interval.
                The height of these pictures is exactly 256 pixels. This field does not exists
                on every videos (these pictures are generated by UploadMediaV2UseCase only).`,
        },
        isVideoNormalized: {
            description: 'Indicate if the video was normalized by the v2 or if it still contains the source video uploaded by the user',
            type: 'boolean',
        },
    },
    required: ['_id', 'category', 'createdAt', 'format', 'socialId', 'type', 'updatedAt', 'urls', 'sizes'],
    definitions: {
        Tag: {
            type: 'object',
            additionalProperties: false,
            properties: {
                language: {
                    enum: Object.values(ApplicationLanguage),
                },
                tag: {
                    type: 'string',
                },
                category: {
                    type: 'string',
                    enum: Object.values(MediaTagCategory),
                },
                subcategory: {
                    type: 'string',
                    enum: [...Object.values(MediaTagSubcategory), null],
                    nullable: true,
                },
            },
            required: ['language', 'tag', 'category'],
            title: 'Tag',
        },
        Dimensions: {
            type: 'object',
            additionalProperties: false,
            properties: {
                original: {
                    $ref: '#/definitions/Dimension',
                },
                normalized: {
                    $ref: '#/definitions/Dimension',
                },
                thumbnail1024Outside: {
                    $ref: '#/definitions/Dimension',
                },
                thumbnail256Outside: {
                    $ref: '#/definitions/Dimension',
                },
                small: {
                    $ref: '#/definitions/Dimension',
                },
                igFit: {
                    $ref: '#/definitions/Dimension',
                },
                cover: {
                    $ref: '#/definitions/Dimension',
                },
                smallCover: {
                    $ref: '#/definitions/Dimension',
                },
                thumbnail: {
                    $ref: '#/definitions/Dimension',
                    description: 'Correspond to the v1 field thumbnail at the root of media',
                },
            },
            required: [],
            title: 'Dimensions',
        },
        Dimension: {
            type: 'object',
            additionalProperties: false,
            properties: {
                width: {
                    type: 'integer',
                },
                height: {
                    type: 'integer',
                },
            },
            required: ['height', 'width'],
            title: 'Dimension',
        },
        ResizeMetadata: {
            type: 'object',
            additionalProperties: false,
            properties: {
                aspectRatio: {
                    type: 'number',
                },
                cropPosition: {
                    $ref: '#/definitions/CropPosition',
                },
                width: {
                    type: 'integer',
                },
                height: {
                    type: 'integer',
                },
            },
            required: ['aspectRatio', 'cropPosition', 'height', 'width'],
            title: 'ResizeMetadata',
        },
        TransformData: {
            type: 'object',
            additionalProperties: false,
            properties: {
                aspectRatio: {
                    enum: Object.values(AspectRatio),
                },
                rotationInDegrees: {
                    type: 'integer',
                },
                left: {
                    type: 'number',
                    description: 'Between 0 and 1, ratio of the image width',
                },
                top: {
                    type: 'number',
                    description: 'Between 0 and 1, ratio of the image height',
                },
                width: {
                    type: 'number',
                    description: 'Between 0 and 1, ratio of the image width',
                },
                height: {
                    type: 'number',
                    description: 'Between 0 and 1, ratio of the image height',
                },
            },
            required: ['aspectRatio', 'rotationInDegrees', 'left', 'top', 'width', 'height'],
            title: 'TransformData',
        },
        CropPosition: {
            type: 'object',
            additionalProperties: false,
            properties: {
                left: {
                    type: 'integer',
                },
                top: {
                    type: 'integer',
                },
            },
            required: ['left', 'top'],
            title: 'CropPosition',
        },
        Sizes: {
            type: 'object',
            description: 'sizes in bytes',
            additionalProperties: false,
            properties: {
                original: {
                    type: 'integer',
                },
                small: {
                    type: 'integer',
                },
                igFit: {
                    type: 'integer',
                },
                _id: {
                    type: 'string',
                },
                cover: {
                    type: 'integer',
                },
                smallCover: {
                    type: 'integer',
                },
            },
            required: [],
            title: 'Sizes',
        },
        Urls: {
            type: 'object',
            additionalProperties: false,
            properties: {
                original: {
                    type: 'string',
                },
                small: {
                    type: 'string',
                },
                igFit: {
                    type: 'string',
                },
                _id: {
                    type: 'string',
                },
                cover: {
                    type: 'string',
                    nullable: true,
                },
                smallCover: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: ['original'],
            title: 'Urls',
        },
        StoredObject: {
            type: 'object',
            additionalProperties: false,
            properties: {
                key: {
                    type: 'string',
                },
                publicUrl: {
                    type: 'string',
                },
                provider: {
                    enum: ['S3'],
                },
            },
            required: ['key', 'publicUrl', 'provider'],
        },
    },
} as const satisfies JSONSchemaExtraProps;

function setCategory(category: any) {
    if (!Object.values(MediaCategory).includes(category?.toLowerCase())) {
        return MediaCategory.ADDITIONAL;
    }
    return category?.toLowerCase() ?? MediaCategory.ADDITIONAL;
}
