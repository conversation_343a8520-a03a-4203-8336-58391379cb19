import { FromSchema } from 'json-schema-to-ts';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { clientJSONSchema } from './client-schema';

const clientSchema = createMongooseSchemaFromJSONSchema(clientJSONSchema);

clientSchema.index({ email: 1, restaurantId: 1 }, { unique: true, partialFilterExpression: { email: { $type: 'string' } } });
clientSchema.index({ phone: 1, restaurantId: 1 }, { unique: true, partialFilterExpression: { phone: { $type: 'object' } } });
clientSchema.index({ email: 1, restaurantId: 1, source: 1 });
clientSchema.index({ phone: 1, restaurantId: 1, source: 1 });
clientSchema.index({ restaurantId: 1, updatedAt: 1, duplicatedFromRestaurantId: 1 });

clientSchema.virtual('duplicatedFromRestaurant', {
    ref: 'Restaurant',
    localField: 'duplicatedFromRestaurantId',
    foreignField: '_id',
    justOne: true,
});

clientSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if (error instanceof MongoError && error.code === 11000) {
        return next({
            duplicateRecordError: true,
            duplicatedKey: Object.keys((error as any).keyPattern).find((key) => key !== 'restaurantId'),
        } as any); // FIXME
    }
    return next();
});

clientSchema.pre('validate', function (next) {
    if (!this.email?.length && !this.phone?.digits) {
        next(new Error('Email or phone required'));
    }

    if (!this.firstName?.length && !this.lastName?.length) {
        next(new Error('Firstname or lastName required'));
    }
    next();
});

export type IClient = FromSchema<
    typeof clientJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const ClientModel = mongoose.model<IClient>(clientJSONSchema.title, clientSchema);
