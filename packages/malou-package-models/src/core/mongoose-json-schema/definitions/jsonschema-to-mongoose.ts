import { Schema, SchemaDefinition, SchemaOptions } from 'mongoose';
import assert from 'node:assert';
import { z } from 'zod';

import { JSONSchemaToMongooseError } from './errors/jsonschema-to-mongoose-error';
import { replaceJSONSchemaRefsByDefinitions } from './helpers/replace-ref-by-definition';

export const extraPropsValidator = z
    .object({
        // keys that exist from JSON Schema
        minLength: z.number().optional(),
        maxLength: z.number().optional(),
        minimum: z.number().optional(),
        maximum: z.number().optional(),

        // keys that do not exist from JSON Schema
        trim: z.boolean().optional(),
        select: z.boolean().optional(),
        lowercase: z.boolean().optional(),
        uppercase: z.boolean().optional(),
        set: z.function().optional(),
        ref: z.string().optional(),
        unique: z.boolean().optional(),
        match: z.instanceof(RegExp).optional(),
        validate: z
            .object({
                validator: z
                    .function()
                    .args(z.any()) // accepts an arbitrary number of arguments
                    .returns(z.boolean()),
                message: z
                    .function()
                    .args(z.any()) // accepts an arbitrary number of arguments
                    .returns(z.string()),
            })
            .optional(),
    })
    .transform((obj) => ({
        // keys that dont need to be mapped
        ...(obj.minLength !== undefined ? { minLength: obj.minLength } : {}),
        ...(obj.maxLength !== undefined ? { maxLength: obj.maxLength } : {}),
        ...(obj.minimum !== undefined ? { min: obj.minimum } : {}),
        ...(obj.maximum !== undefined ? { max: obj.maximum } : {}),

        ...(obj.trim !== undefined ? { trim: obj.trim } : {}),
        ...(obj.select !== undefined ? { select: obj.select } : {}),
        ...(obj.lowercase !== undefined ? { lowercase: obj.lowercase } : {}),
        ...(obj.uppercase !== undefined ? { uppercase: obj.uppercase } : {}),
        ...(obj.set !== undefined ? { set: obj.set } : {}),
        ...(obj.ref !== undefined ? { ref: obj.ref } : {}),
        ...(obj.unique !== undefined ? { unique: obj.unique } : {}),
        ...(obj.match !== undefined ? { match: obj.match } : {}),
        ...(obj.validate !== undefined ? { validate: obj.validate } : {}),
    }));

function getJSONSchemaKeyword(key: string, properties: any): 'type' | 'anyOf' | 'enum' {
    if (properties[key].type) {
        return 'type';
    }
    if (properties[key].anyOf) {
        return 'anyOf';
    }
    if (properties[key].enum) {
        return 'enum';
    }
    throw new JSONSchemaToMongooseError(`Miss keyword "type" or "anyOf" or "enum" for the field ${key}`);
}

function handleAnyOf(anyOf: any[], field: string, properties: any): { valueOfTheField: any; type: string } {
    const anyOfFilterOutNull = anyOf.filter((anyOfElem: any) => anyOfElem.type !== 'null');
    const anyOfTypes = anyOfFilterOutNull.map((anyOfElem: any) => anyOfElem.type);
    const uniqueAnyOfTypes = [...new Set(anyOfTypes)];
    if (uniqueAnyOfTypes.length !== 1) {
        throw new JSONSchemaToMongooseError(
            `There are multiple types in keyword anyOf for the field ${field}, don't know how to handle between ${uniqueAnyOfTypes.join(
                ', '
            )}`
        );
    }
    const type = uniqueAnyOfTypes[0];
    // this line is useful to keep other props like "format", "default" ..
    const newProperties = {
        ...properties[field],
        ...anyOf.find((anyOfElem: any) => anyOfElem.type === type),
    };
    return { valueOfTheField: newProperties, type };
}

function handleEnum(field: string, properties: any): { valueOfTheField: any; type: string } {
    return {
        valueOfTheField: properties[field],
        type: 'enum',
    };
}

function handleType(field: string, properties: any): { valueOfTheField: any; type: string } {
    const { type } = properties[field];
    if (type === 'array') {
        if (!properties[field].items) {
            throw new JSONSchemaToMongooseError(`Miss keyword "items" for the field ${field} which is an Array`);
        }
        if (properties[field].items.anyOf) {
            const { anyOf } = properties[field].items;
            const { valueOfTheField } = handleAnyOf(anyOf, field, properties[field]);
            return {
                valueOfTheField: {
                    ...properties[field],
                    items: valueOfTheField,
                },
                type: 'array',
            };
        }
        if (properties[field].items.enum) {
            return {
                valueOfTheField: properties[field],
                type: 'array',
            };
        }
    }
    return {
        valueOfTheField: properties[field],
        type,
    };
}

function handleKeywordStrategy(keyword: 'type' | 'anyOf' | 'enum', field: string, properties: any): { valueOfTheField: any; type: string } {
    switch (keyword) {
        case 'type':
            return handleType(field, properties);
        case 'anyOf':
            return handleAnyOf(properties[field].anyOf, field, properties);
        case 'enum':
            return handleEnum(field, properties);
        default:
            throw new JSONSchemaToMongooseError(`Unknown keyword ${keyword} for the field ${field}`);
    }
}

function getTypeByCheckingFormat(
    type: string,
    properties: object
): 'array' | 'boolean' | 'date' | 'enum' | 'integer' | 'number' | 'object' | 'objectId' | 'string' {
    if (type === 'string') {
        if ('format' in properties) {
            const result = z
                .union([
                    z.literal('date-time').transform((): 'date' => 'date'),
                    z.literal('integer').transform((): 'string' => 'string'),
                    z.literal('objectId'),
                    z.literal('uri').transform((): 'string' => 'string'),
                    z.literal('date-time-nullable').transform((): 'date' => 'date'),
                ])
                .safeParse(properties.format);
            if (result.success) {
                return result.data;
            }
            throw new Error(`Invalid format (properties: ${JSON.stringify(properties)})`);
        }
    }
    if ('enum' in properties) {
        assert(Array.isArray(properties.enum));
        return 'enum';
    }
    const result = z.enum(['objectId', 'date', 'object', 'array', 'string', 'number', 'integer', 'boolean']).safeParse(type);
    if (result.success) {
        return result.data;
    }
    throw new Error(`Invalid type (was ${JSON.stringify(type)} with properties ${JSON.stringify(properties)})`);
}

const TYPE_MAP: { [key: string]: any } = {
    string: String,
    number: Number,
    integer: Number,
    boolean: Boolean,
    date: Date,

    objectId: Schema.Types.ObjectId,
    mixed: Schema.Types.Mixed,
    array: 'does_not_exist_in_mongoose_schema',
    enum: 'does_not_exist_in_mongoose_schema',
};

export function createMongooseSchemaFromJSONSchema(jsonTsVariable: any): Schema {
    function mapToMongooseSchema(jsonSchema: any): Schema {
        const mongooseSchemaDefinition: SchemaDefinition = {};

        if (!jsonSchema.properties) {
            throw new JSONSchemaToMongooseError('Miss keyword "properties" in JSON Schema');
        }

        Object.keys(jsonSchema.properties).forEach((key) => {
            if (['_id', 'createdAt', 'updatedAt'].includes(key)) {
                return;
            }

            const jsonSchemaKeyword = getJSONSchemaKeyword(key, jsonSchema.properties);
            const strategy = handleKeywordStrategy(jsonSchemaKeyword, key, jsonSchema.properties);
            let { type } = strategy;
            const { valueOfTheField } = strategy;

            type = getTypeByCheckingFormat(type, valueOfTheField);
            let partialSchema;

            if (type === 'object') {
                if (!valueOfTheField.properties) {
                    throw new JSONSchemaToMongooseError(`Miss keyword "properties" for the field ${key} which is an Object!`);
                }

                if (Object.keys(valueOfTheField.properties).length === 0 && valueOfTheField.additionalProperties) {
                    partialSchema = {
                        type: TYPE_MAP.mixed,
                    };
                } else {
                    const nestedSchema = mapToMongooseSchema(valueOfTheField);
                    partialSchema = {
                        type: nestedSchema,
                    };
                }
            } else if (type === 'array') {
                type = getTypeByCheckingFormat(type, valueOfTheField);
                const typeOfTheArray = getTypeByCheckingFormat(valueOfTheField.items.type, valueOfTheField.items);

                if (!typeOfTheArray) {
                    throw new JSONSchemaToMongooseError(`Miss keyword "type" inside "items" for the field ${key} which is an Array!`);
                }

                const defaultValue = valueOfTheField.items.default;
                const otherProps = extraPropsValidator.parse(valueOfTheField.items);

                if (typeOfTheArray === 'object') {
                    const arrayItemSchema = mapToMongooseSchema(valueOfTheField.items);
                    partialSchema = {
                        type: [arrayItemSchema],
                    };
                } else if (typeOfTheArray === 'array') {
                    const arrayItemSchema = mapToMongooseSchema(valueOfTheField.items.items);
                    partialSchema = {
                        type: [[arrayItemSchema]],
                    };
                } else if (typeOfTheArray === 'enum') {
                    partialSchema = {
                        type: [
                            {
                                type: TYPE_MAP.string,
                                enum: valueOfTheField.items.enum,
                                ...(defaultValue !== undefined ? { default: defaultValue } : {}),
                                ...(otherProps as any),
                            },
                        ],
                    };
                } else {
                    const mappedType = TYPE_MAP[typeOfTheArray];

                    partialSchema = {
                        type: [
                            {
                                type: mappedType,
                                ...(defaultValue !== undefined ? { default: defaultValue } : {}),
                                ...(otherProps as any),
                            },
                        ],
                    };
                }
            } else if (type === 'enum') {
                partialSchema = {
                    type: TYPE_MAP.string,
                    enum: valueOfTheField.enum,
                };
            } else {
                // TODO: HANDLE ARRAY OF DATE DEFAULT VALUE
                const mappedType = TYPE_MAP[type];

                if (!mappedType) {
                    throw new JSONSchemaToMongooseError(
                        `Wrong declaration for the field : ${key} ! Type ${type} not handled, only ${Object.keys(TYPE_MAP).join(
                            ', '
                        )} are handled`
                    );
                }

                partialSchema = {
                    type: mappedType,
                };
            }

            // handle required, default value and other props
            const required = jsonSchema.required?.includes(key) ?? false;
            const defaultKey = 'default';
            const doesDefaultKeyExists = Object.prototype.hasOwnProperty.call(valueOfTheField, defaultKey);
            const defaultValue = valueOfTheField[defaultKey];
            const otherProps = extraPropsValidator.parse(valueOfTheField);

            mongooseSchemaDefinition[key] = {
                ...partialSchema,
                required,
                ...(doesDefaultKeyExists ? { default: defaultValue } : {}),
                ...(otherProps as any),
            };
        });

        // Compute schema options
        const keys = Object.keys(jsonSchema.properties);

        // Don't define options if discriminator (will inherit from parent)
        const mongooseSchemaOptions: SchemaOptions | undefined = !jsonSchema.discriminator
            ? {
                  timestamps: keys.includes('createdAt') || keys.includes('updatedAt'),
                  _id: keys.includes('_id'),
                  toJSON: { virtuals: true },
                  toObject: { virtuals: true },
              }
            : undefined;

        const schema = new Schema(mongooseSchemaDefinition, mongooseSchemaOptions);

        return schema;
    }
    return mapToMongooseSchema(replaceJSONSchemaRefsByDefinitions(jsonTsVariable));
}
