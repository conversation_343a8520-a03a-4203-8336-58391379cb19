import { globSync } from 'glob';
import mongoose, { Types } from 'mongoose';
import path from 'path';

import { capitalize, emailRegex, urlRegex } from '@malou-io/package-utils';

import { createMongooseSchemaFromJSONSchema } from './jsonschema-to-mongoose';

const _randomModelName = () => (Math.random() + 1).toString(36).substring(7);
const toCamelCase = (str: string) => str.replace(/-([a-z])/g, (_, char) => char.toUpperCase());

describe('createMongooseSchemaFromJSONSchema', () => {
    describe('test schema creation', () => {
        const pathVarJSONSchema = path.join(__dirname, '..', '..', '..', 'modules', '*', '*-schema.ts');
        const jsonSchemas = globSync(pathVarJSONSchema, { ignore: 'node_modules/**' });
        const pathDiscriminatorVarJSONSchema = path.join(__dirname, '..', '..', '..', 'modules', '*', 'discriminators', '*', '*-schema.ts');
        const discriminatorJsonSchemas = globSync(pathDiscriminatorVarJSONSchema, { ignore: 'node_modules/**' });

        it('should throw JSONSchemaToMongooseError : `how to handle between multiple types`', async () => {
            try {
                createMongooseSchemaFromJSONSchema({
                    $schema: 'http://json-schema.org/draft-07/schema#',
                    $id: 'https://example.com/person.schema.json',
                    title: 'Person',
                    description: 'A person',
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'string',
                        },
                        lastConnection: {
                            anyOf: [
                                {
                                    type: 'string',
                                    format: 'date-time',
                                },
                                {
                                    type: 'number',
                                },
                            ],
                        },
                    },
                });
                expect(true).toBe(false);
            } catch (e: any) {
                expect(e.message).toContain('how to handle between');
            }
        });

        it('should throw JSONSchemaToMongooseError : `Miss keyword items` for array type', async () => {
            try {
                createMongooseSchemaFromJSONSchema({
                    $schema: 'http://json-schema.org/draft-07/schema#',
                    $id: 'https://example.com/person.schema.json',
                    title: 'Person',
                    description: 'A person',
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'string',
                        },
                        hobbies: {
                            type: 'array',
                        },
                    },
                });
                expect(true).toBe(false);
            } catch (e: any) {
                expect(e.message).toContain(`Miss keyword "items"`);
            }
        });

        it('should throw with an JSONSchemaToMongooseError : `how to handle between multiple types`', async () => {
            try {
                createMongooseSchemaFromJSONSchema({
                    $schema: 'http://json-schema.org/draft-07/schema#',
                    $id: 'https://example.com/person.schema.json',
                    title: 'Person',
                    description: 'A person',
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'string',
                        },
                        hobbies: {
                            type: 'toto',
                        },
                    },
                });
                expect(true).toBe(false);
            } catch (e: any) {
                expect(e.message).toContain('Invalid type (was "toto" with properties {"type":"toto"})');
            }
        });

        it('should set the ref', async () => {
            const schema = createMongooseSchemaFromJSONSchema({
                $schema: 'http://json-schema.org/draft-07/schema#',
                $id: 'https://example.com/person.schema.json',
                title: 'Person',
                description: 'A person',
                type: 'object',
                properties: {
                    _id: {
                        type: 'string',
                    },
                    restaurantId: {
                        type: 'string',
                        format: 'objectId',
                        ref: 'Restaurant',
                    },
                },
            });

            expect(schema.obj.restaurantId).toMatchObject({
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Restaurant',
            });
        });

        it.each(jsonSchemas)('should create mongooseSchema for %s', async (jsonSchemaPath) => {
            const match = jsonSchemaPath.match(/\/([^/]+)-schema\.ts$/);
            if (!match) throw new Error(`Invalid schema path: ${jsonSchemaPath}`);

            const baseName = toCamelCase(match[1]); // e.g. wheelOfFortune
            const nameOfTheSchema = `${baseName}JSONSchema`; // e.g. wheelOfFortuneJSONSchema

            // eslint-disable-next-line import/no-dynamic-require, global-require, @typescript-eslint/no-var-requires
            const required = require(jsonSchemaPath);
            const schemaVar = required[nameOfTheSchema];

            if (!schemaVar) {
                throw new Error(`Export ${nameOfTheSchema} not found in ${jsonSchemaPath}`);
            }

            const schema = createMongooseSchemaFromJSONSchema(schemaVar);
            expect(Object.keys(schema.obj).length).toBeGreaterThanOrEqual(1);
        });

        it.each(discriminatorJsonSchemas)('should create mongooseSchema (discriminator) for %s', async (jsonSchemaPath) => {
            const match = jsonSchemaPath.match(/\/([^/]+)-schema\.ts$/);
            if (!match) throw new Error(`Invalid schema path: ${jsonSchemaPath}`);

            const baseName = toCamelCase(match[1]); // e.g. wheelOfFortune
            const nameOfTheSchema = `${baseName}JSONSchema`; // e.g. wheelOfFortuneJSONSchema

            // eslint-disable-next-line import/no-dynamic-require, global-require, @typescript-eslint/no-var-requires
            const required = require(jsonSchemaPath);
            const schemaVar = required[nameOfTheSchema];

            if (!schemaVar) {
                throw new Error(`Export ${nameOfTheSchema} not found in ${jsonSchemaPath}`);
            }

            const schema = createMongooseSchemaFromJSONSchema(schemaVar);
            expect(Object.keys(schema.obj).length).toBeGreaterThanOrEqual(1);
        });
    });

    it('should create a Person document with default values', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: {
                    type: 'string',
                },
                firstName: {
                    type: 'string',
                    default: 'Fantin',
                },
                age: {
                    type: 'number',
                    default: 18,
                },
                verified: {
                    type: 'boolean',
                    default: false,
                },
                hobbies: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    default: ['hello'],
                },
                numbers: {
                    type: 'array',
                    items: {
                        type: 'number',
                    },
                    default: [1, 2, 3],
                },
                lastConnection: {
                    type: 'string',
                    format: 'date-time',
                    default() {
                        return new Date();
                    },
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const doc = await personModel.create({});

        expect(doc).toMatchObject({
            age: 18,
            verified: false,
            hobbies: ['hello'],
            numbers: [1, 2, 3],
        });
        expect(doc.lastConnection).toBeInstanceOf(Date);
    });

    it('should create a Person document with the specified firstName', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: {
                    type: 'string',
                },
                firstName: {
                    type: 'string',
                    default: 'Fantin',
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const doc = await personModel.create({
            firstName: 'Rafik',
        });

        expect(doc).toMatchObject({
            firstName: 'Rafik',
        });
    });

    it('should throw validation error when we dont specify a required field', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: {
                    type: 'string',
                },
                firstName: {
                    type: 'string',
                    description: "The person's first name.",
                },
            },
            required: ['_id', 'firstName'],
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({});
            expect(true).toBe(false);
        } catch (e: any) {
            expect(e.message).toContain('firstName');
        }
    });

    it('should not throw a validation error on firstName field because it has a default value', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: {
                    type: 'string',
                },
                firstName: {
                    type: 'string',
                    default: 'Fantin',
                },
            },
            required: ['_id', 'firstName'],
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const doc = await personModel.create({});

        expect(doc).toMatchObject({
            firstName: 'Fantin',
        });
    });

    it('should throw mongoose error about the missing _id', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                firstName: {
                    type: 'string',
                    description: "The person's first name.",
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({});
            expect(true).toBe(false);
        } catch (e: any) {
            expect(e.message).toBe('document must have an _id before saving');
        }
    });

    it('should handle nested schema', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                access: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Access',
                    },
                },
            },
            required: ['access'],
            definitions: {
                Access: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        platformKey: {
                            type: 'string',
                        },
                        status: {
                            type: 'string',
                        },
                        accessType: {
                            type: 'string',
                        },
                        lastUpdated: {
                            type: 'string',
                            format: 'date-time',
                        },
                        lastVerified: {
                            anyOf: [
                                {
                                    type: 'string',
                                    format: 'date-time',
                                },
                                {
                                    type: 'null',
                                },
                            ],
                        },
                        active: {
                            type: 'boolean',
                        },
                        data: {
                            $ref: '#/definitions/Data',
                        },
                    },
                    required: ['accessType', 'active', 'lastUpdated', 'lastVerified', 'platformKey', 'status'],
                    title: 'Access',
                },
                Data: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        login: {
                            type: 'string',
                        },
                        password: {
                            anyOf: [
                                {
                                    type: 'null',
                                },
                                {
                                    type: 'string',
                                },
                            ],
                        },
                    },
                    required: ['login', 'password'],
                    title: 'Data',
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);
        const personPayload = {
            access: [
                {
                    platformKey: 'platformKey',
                    status: 'status',
                    accessType: 'accessType',
                    lastUpdated: new Date('2021-09-29T14:16:54.000Z'),
                    lastVerified: new Date('2021-09-29T14:16:54.000Z'),
                    active: true,
                    data: {
                        login: 'login',
                        password: 'strongPassword',
                    },
                },
                {
                    platformKey: 'platformKey',
                    status: 'status',
                    accessType: 'accessType',
                    lastUpdated: new Date('2021-09-29T14:16:54.000Z'),
                    lastVerified: new Date('2021-09-29T14:16:54.000Z'),
                    active: true,
                },
            ],
        };
        const doc = await personModel.create(personPayload);

        expect(doc).toMatchObject(personPayload);
    });

    it('should allow mixed schema when additionalProperties is set to true', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                attributes: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['attributes'],
        });

        const personModel = mongoose.model(_randomModelName(), schema);
        const personPayload = {
            attributes: {
                platformKey: 'platformKey',
                status: 'status',
                accessType: 'accessType',
                lastUpdated: new Date('2021-09-29T14:16:54.000Z'),
                lastVerified: new Date('2021-09-29T14:16:54.000Z'),
                active: true,
                data: {
                    login: 'login',
                    password: 'strongPassword',
                },
            },
        };
        const doc = await personModel.create(personPayload);
        expect(doc).toMatchObject(personPayload);

        const person2Payload = {
            attributes: {
                platformKey: 'platformKey',
                status: 'status',
                accessType: 'accessType',
                bonjour: {
                    bien: 'ou bien ?',
                },
            },
        };
        const doc2 = await personModel.create(person2Payload);
        expect(doc2).toMatchObject(person2Payload);
    });

    it('should prevent mixed schema when additionalProperties is set to false', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                attributes: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {},
                },
            },
            required: ['attributes'],
        });

        const personModel = mongoose.model(_randomModelName(), schema);
        const personPayload = {
            attributes: {
                platformKey: 'platformKey',
                status: 'status',
                accessType: 'accessType',
                lastUpdated: new Date('2021-09-29T14:16:54.000Z'),
                lastVerified: new Date('2021-09-29T14:16:54.000Z'),
                active: true,
                data: {
                    login: 'login',
                    password: 'strongPassword',
                },
            },
        };
        const doc = await personModel.create(personPayload);
        expect(doc.attributes).toMatchObject({});
    });

    it('should throw an error when we create a Person document with a wrong enum value', async () => {
        enum Role {
            Admin = 'admin',
            Basic = 'basic',
            VIP = 'vip',
        }
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                role: {
                    enum: Object.values(Role),
                },
            },
            required: ['role'],
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({
                role: 'wrong',
            });
            expect(true).toBe(false);
        } catch (e: any) {
            expect(e.message).toContain('role');
        }
    });

    it('should use the set method specified in the JSONSchema ', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                    trim: true,
                    set: capitalize,
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const doc = await personModel.create({
            firstName: 'viCtor',
        });

        expect(doc).toMatchObject({
            firstName: 'Victor',
        });
    });

    it('should throw a validation error about the email', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                email: {
                    type: 'string',
                    match: emailRegex,
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({
                email: '<EMAIL>',
            });
            expect(true).toBe(true);
        } catch (err: any) {
            expect(err.message).toContain('zzz');
        }
    });

    it('should throw a validation error about the URL', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/post.schema.json',
            title: 'Post',
            description: 'A post',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                url: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
            },
        });

        const postModel = mongoose.model(_randomModelName(), schema);

        try {
            await postModel.create({
                url: 'htp:abraba',
            });
            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain('url');
        }
    });

    it('should pass the regex validation about the url', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/post.schema.json',
            title: 'Post',
            description: 'A post',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                url: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
            },
        });

        const postModel = mongoose.model(_randomModelName(), schema);

        try {
            await postModel.create({
                url: 'https://malou.io',
            });
            expect(true).toBe(true);
        } catch (err: any) {
            expect(true).toBe(false);
        }
    });

    it('should throw a validation error about the length of the firstName', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                    minLength: 5,
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({
                firstName: 'toto',
            });
            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain('firstName');
        }
    });

    it('should throw a validation error about the value of an integer', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            $id: 'https://example.com/person.schema.json',
            title: 'Review',
            description: 'A review',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                numberOfStars: {
                    type: 'integer',
                    minimum: 1,
                    maximum: 5,
                },
            },
        });

        const reviewModel = mongoose.model(_randomModelName(), schema);

        try {
            await reviewModel.create({
                numberOfStars: 7,
            });
            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain('numberOfStars');
        }
    });

    it('should not add an extra _id field for nested object', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                address: {
                    $ref: '#/definitions/Address',
                },
            },
            definitions: {
                Address: {
                    type: 'object',
                    properties: {
                        street: {
                            type: 'string',
                        },
                    },
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const doc = await personModel.create({
            firstName: 'toto',
            address: {
                street: 'rue de la paix',
            },
        });

        expect(doc.address._id).toBe(undefined);
    });

    it('should throw when passing string instead of passing ObjectId', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                addresses: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({
                firstName: 'toto',
                addresses: ['NOT_AN_OBJECT_ID'],
            });
            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain('addresses.0: Cast to [ObjectId]');
        }
    });

    it('should create Person document with restaurantId as ObjectId and TemplateIds as Array of ObjectId', async () => {
        const personSchema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                restaurant: {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Restaurant',
                },
                templateIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                        ref: 'Template',
                    },
                },
            },
        });

        const restaurantSchema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Restaurant',
            description: 'A restaurant',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                name: {
                    type: 'string',
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), personSchema);
        const restaurantModel = mongoose.model('Restaurant', restaurantSchema);

        const restaurantData = await restaurantModel.create({
            name: 'Sushi & Co',
        });
        const person = await personModel.create({
            firstName: 'toto',
            restaurant: restaurantData._id,
            templateIds: [new Types.ObjectId()],
        });

        expect(person).toMatchObject({
            firstName: 'toto',
            restaurant: restaurantData._id,
            templateIds: [expect.any(Types.ObjectId)],
        });

        const { restaurant } = await person.populate('restaurant');
        expect(restaurant).toMatchObject({
            _id: restaurantData._id,
            name: 'Sushi & Co',
        });
    });

    it('should populate data as array of ObjectId, and preserve default and trim values', async () => {
        const personSchema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                posts: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                        ref: 'Post',
                    },
                },
                restaurantId: {
                    type: 'string',
                    format: 'objectId',
                },
            },
        });
        const postSchema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Post',
            description: 'A post',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                text: {
                    type: 'string',
                },
                followerEmails: {
                    type: 'array',
                    items: {
                        type: 'string',
                        trim: true,
                        lowercase: true,
                    },
                    default: [],
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), personSchema);
        const postModel = mongoose.model('Post', postSchema);

        const post1 = await postModel.create({
            text: 'Hey you',
        });
        const post2 = await postModel.create({
            text: 'Salut la commu',
            followerEmails: ['<EMAIL>', '<EMAIL>', '   <EMAIL>  '],
        });

        const person = await personModel.create({
            firstName: 'toto',
            restaurantId: new Types.ObjectId(),
            posts: [post1._id, post2._id],
        });
        expect(person).toMatchObject({
            firstName: 'toto',
            restaurantId: expect.any(Types.ObjectId),
            posts: [post1._id, post2._id],
        });

        // Check Post default, and trim
        expect(post1.followerEmails).toEqual([]);
        expect(post2.followerEmails).toEqual(['<EMAIL>', '<EMAIL>', '<EMAIL>']);

        // Check populate
        const { posts } = await person.populate('posts');
        expect(posts[0]).toMatchObject({
            _id: post1._id,
            text: 'Hey you',
        });
        expect(posts[1]).toMatchObject({
            _id: post2._id,
            text: 'Salut la commu',
        });
    });

    it('should throw validation error when we specify pattern in nested schema ', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                settings: {
                    $ref: '#/definitions/Settings',
                },
            },
            definitions: {
                Settings: {
                    type: 'object',
                    properties: {
                        email: {
                            type: 'string',
                            match: emailRegex,
                        },
                    },
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        try {
            await personModel.create({
                firstName: 'toto',
                settings: {
                    email: 'fantin.com',
                },
            });
            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain('settings.email');
        }
    });

    it('should create a person with default nested settings ', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                },
                settings: {
                    $ref: '#/definitions/Settings',
                    default: {
                        receiveFeedbacks: true,
                        notificationSettings: {
                            userDevicesTokens: [],
                            active: true,
                            reviews: {
                                active: true,
                                realtime: true,
                                receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                                concernedRatings: [1, 2, 3, 4, 5],
                                includeAutoRepliedReviews: true,
                            },
                            messages: {
                                active: true,
                                realtime: true,
                                receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                            },
                            posts: {
                                noMoreScheduledPosts: {
                                    active: true,
                                    lastNotificationSentDate() {
                                        return new Date();
                                    },
                                },
                                publishError: {
                                    active: true,
                                },
                            },
                        },
                        receiveMessagesNotifications: {
                            active: true,
                            restaurantsIds: [],
                        },
                    },
                },
            },
            definitions: {
                Settings: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        receiveFeedbacks: {
                            type: 'boolean',
                        },
                        notificationSettings: {
                            $ref: '#/definitions/NotificationSettings',
                        },
                        receiveMessagesNotifications: {
                            $ref: '#/definitions/ReceiveMessagesNotifications',
                        },
                        updatedAt: {
                            type: 'string',
                            format: 'date-time',
                        },
                        createdAt: {
                            type: 'string',
                            format: 'date-time',
                        },
                    },
                    required: ['notificationSettings', 'receiveFeedbacks', 'receiveMessagesNotifications'],
                    title: 'Settings',
                },
                NotificationSettings: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        userDevicesTokens: {
                            type: 'array',
                            items: {
                                type: 'string',
                            },
                        },
                        active: {
                            type: 'boolean',
                        },
                        reviews: {
                            $ref: '#/definitions/Reviews',
                        },
                        messages: {
                            $ref: '#/definitions/Messages',
                        },
                    },
                    required: ['active', 'messages', 'reviews', 'userDevicesTokens'],
                    title: 'NotificationSettings',
                },
                Messages: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        active: {
                            type: 'boolean',
                        },
                        realtime: {
                            type: 'boolean',
                        },
                        receivingWeekDays: {
                            type: 'array',
                            items: {
                                type: 'integer',
                            },
                        },
                    },
                    required: ['active', 'realtime', 'receivingWeekDays'],
                    title: 'Messages',
                },
                Reviews: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        active: {
                            type: 'boolean',
                        },
                        realtime: {
                            type: 'boolean',
                        },
                        receivingWeekDays: {
                            type: 'array',
                            items: {
                                type: 'integer',
                            },
                        },
                        concernedRatings: {
                            type: 'array',
                            items: {
                                type: 'integer',
                            },
                        },
                        includeAutoRepliedReviews: {
                            type: 'boolean',
                        },
                    },
                    required: ['active', 'concernedRatings', 'includeAutoRepliedReviews', 'realtime', 'receivingWeekDays'],
                    title: 'Reviews',
                },
                ReceiveMessagesNotifications: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        active: {
                            type: 'boolean',
                        },
                        restaurantsIds: {
                            type: 'array',
                            items: {
                                type: 'string',
                                format: 'objectId',
                            },
                        },
                    },
                    required: ['active', 'restaurantsIds'],
                    title: 'ReceiveMessagesNotifications',
                },
                Posts: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        noMoreScheduledPosts: {
                            $ref: '#/definitions/NoMoreScheduledPosts',
                        },
                        publishError: {
                            $ref: '#/definitions/PublishError',
                        },
                    },
                    required: ['noMoreScheduledPosts', 'publishError'],
                    title: 'Posts',
                },
                NoMoreScheduledPosts: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        active: {
                            type: 'boolean',
                        },
                        lastNotificationSentDate: {
                            type: 'string',
                            format: 'date-time',
                        },
                    },
                    required: ['active', 'lastNotificationSentDate'],
                    title: 'NoMoreScheduledPosts',
                },
                PublishError: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        active: {
                            type: 'boolean',
                        },
                    },
                    required: ['active'],
                    title: 'PublishError',
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);

        const person = await personModel.create({
            firstName: 'toto',
        });

        expect(person._doc.settings).toMatchObject({
            receiveFeedbacks: true,
            notificationSettings: {
                userDevicesTokens: [],
                active: true,
                reviews: {
                    active: true,
                    realtime: true,
                    receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                    concernedRatings: [1, 2, 3, 4, 5],
                    includeAutoRepliedReviews: true,
                },
                messages: {
                    active: true,
                    realtime: true,
                    receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                },
            },
            receiveMessagesNotifications: {
                active: true,
                restaurantsIds: [],
            },
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
        });
    });

    it('should check if validate works ', async () => {
        const schema = createMongooseSchemaFromJSONSchema({
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: 'Person',
            description: 'A person',
            type: 'object',
            properties: {
                _id: { type: 'string', format: 'objectId' },
                firstName: {
                    type: 'string',
                    trim: true,
                    // TODO: check this
                    validate: {
                        validator(v: string): boolean {
                            return v ? v.length >= 0 && v.length <= 10 : true;
                        },
                        message: (props: any) => `Description must be between 0 and 10 characters (currently ${props.value.length})`,
                    },
                },
            },
        });

        const personModel = mongoose.model(_randomModelName(), schema);
        const nameTooLong = 'a name too long';
        try {
            await personModel.create({
                firstName: 'a name too long',
            });

            expect(true).toBe(false);
        } catch (err: any) {
            expect(err.message).toContain(`Description must be between 0 and 10 characters (currently ${nameTooLong.length})`);
        }
    });
});
