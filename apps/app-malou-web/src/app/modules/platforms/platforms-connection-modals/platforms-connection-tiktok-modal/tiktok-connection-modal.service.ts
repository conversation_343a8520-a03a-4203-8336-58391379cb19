import { inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, switchMap } from 'rxjs';

import { PlatformKey } from '@malou-io/package-utils';

import { TikTokConnectionStep0Component } from ':modules/platforms/platforms-connection-modals/platforms-connection-tiktok-modal/tiktok-connection-step-0/tiktok-connection-step-0.component';
import {
    PARENT_STEPPER_COMPONENT_PREFERRED_HEIGHT,
    PARENT_STEPPER_COMPONENT_PREFERRED_WIDTH,
    ParentStepperComponentDialogData,
    PlatformsConnectionParentStepperComponent,
} from ':modules/platforms/platforms-connection-modals/shared/platforms-connection-parent-stepper/platforms-connection-parent-stepper.component';
import { createStep } from ':modules/platforms/platforms-connection-modals/shared/platforms-connection-parent-stepper/step.interface';
import { GetStepIndexFromCredentialService } from ':modules/platforms/platforms-connection-modals/shared/services/get-step-index-from-credential.service';
import { SelectCredentialAndBusinessStepComponent } from ':modules/platforms/platforms-connection-modals/shared/steps/select-credential-and-business-step/select-credential-and-business-step.component';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

const NOT_CONNECTED_STEP_INDEX = 0;
const CONNECTED_STEP_INDEX = 1;

export interface TikTokConnectionModalResult {
    hasDataChanged?: boolean;
}

@Injectable({ providedIn: 'root' })
export class TikTokConnectionModalService {
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _translateService = inject(TranslateService);
    private readonly _getStepIndexFromCredentialService = inject(GetStepIndexFromCredentialService);

    open(): Observable<TikTokConnectionModalResult | undefined> {
        return this._getStepIndexFromCredentialService.execute(PlatformKey.TIKTOK, NOT_CONNECTED_STEP_INDEX, CONNECTED_STEP_INDEX).pipe(
            switchMap((stepIndex) =>
                this._customDialogService
                    .open<
                        PlatformsConnectionParentStepperComponent,
                        ParentStepperComponentDialogData<undefined, TikTokConnectionModalResult>,
                        TikTokConnectionModalResult
                    >(PlatformsConnectionParentStepperComponent, {
                        height: PARENT_STEPPER_COMPONENT_PREFERRED_HEIGHT,
                        width: PARENT_STEPPER_COMPONENT_PREFERRED_WIDTH,
                        data: {
                            steps: [
                                {
                                    stepName: this._translateService.instant('platforms.connection_new.deliveroo.step_0.step_name'),
                                    component: TikTokConnectionStep0Component,
                                    componentInputs: {},
                                },
                                createStep(
                                    this._translateService.instant('platforms.connection_new.deliveroo.step_1.step_name'),
                                    SelectCredentialAndBusinessStepComponent,
                                    {
                                        titleTranslationKey: this._translateService.instant(
                                            'platforms.connection_new.deliveroo.step_1.step_name'
                                        ),
                                        goToStepParam: { type: 'relative', value: -1 },
                                        platformKey: PlatformKey.TIKTOK,
                                        showNewOauthButton: false,
                                        showPlatformIconInModalTitle: false,
                                    }
                                ),
                            ],
                            stepperTitle: this._translateService.instant('platforms.connection_new.tiktok.stepper.title'),
                            stepperSubtitle: this._translateService.instant('platforms.connection_new.tiktok.stepper.subtitle'),
                            stepperPlatformKeyIcon: PlatformKey.TIKTOK,
                            stepIndex: stepIndex,
                        },
                    })
                    .afterClosed()
            )
        );
    }
}
