<div class="flex justify-center">
    <div class="flex flex-col items-center">
        <div class="relative">
            <span
                class="malou-text-42--bold md:malou-text-28--bold text-color-text-title text-center font-passion-one uppercase md:leading-tight"
                [innerHTML]="'ranking.event.title' | translate"></span>
            <mat-icon
                class="absolute -right-7 bottom-[40px] !h-10 !w-10 shrink-0 text-malou-pink md:!h-7 md:!w-7"
                [svgIcon]="SvgIcon.SPARKLES"></mat-icon>
        </div>
        <span class="malou-text-28--semibold md:malou-text-14--bold text-center text-malou-text">
            {{ 'ranking.event.subtitle' | translate: { eventName: mappedEventName() } }}</span
        >
    </div>
</div>

<div class="px-28">
    <ng-container [ngTemplateOutlet]="firstRanksTemplate"></ng-container>
    <div class="mb-28 mt-4 flex flex-col gap-2" #scrollContainer>
        @for (diag of diagnosticsDataToDisplay(); track diag.id; let i = $index) {
            <ng-container [ngTemplateOutlet]="diagnosticScoreTemplate" [ngTemplateOutletContext]="{ diagnostic: diag }"></ng-container>
        }
    </div>
</div>

<ng-template #firstRanksTemplate>
    <div class="relative flex items-center justify-center">
        <ng-container
            [ngTemplateOutlet]="secondOrThirdTemplate"
            [ngTemplateOutletContext]="{
                image: top3DiagnosticsImages()[1],
                rank: 2,
                diagnostic: diagnosticsDataToDisplay()[1],
            }"></ng-container>
        <ng-container
            [ngTemplateOutlet]="firstRankTemplate"
            [ngTemplateOutletContext]="{
                diagnostic: diagnosticsDataToDisplay()[0],
            }"></ng-container>
        <ng-container
            [ngTemplateOutlet]="secondOrThirdTemplate"
            [ngTemplateOutletContext]="{
                image: top3DiagnosticsImages()[2],
                rank: 3,
                diagnostic: diagnosticsDataToDisplay()[2],
            }"></ng-container>
    </div>
</ng-template>

<ng-template let-diagnostic="diagnostic" #firstRankTemplate>
    <div
        class="relative z-10"
        [ngClass]="{
            'fade-in-animation': top3DiagnosticsImages()[0] === DiagnosticTop3RanksImages.NEW_FIRST,
        }">
        @if (diagnostic) {
            <div class="absolute left-3 top-12 flex h-full w-full items-center justify-center">
                <div class="flex h-[240px] flex-col items-center justify-center gap-1">
                    <div class="flex flex-col items-center gap-1 pt-3">
                        <span
                            class="malou-text-20--semibold line-clamp-2 w-[250px] text-ellipsis break-words text-center text-malou-text-title">
                            {{ diagnostic.restaurantName }}
                        </span>
                        <span
                            class="malou-text-12--semibold line-clamp-2 w-[250px] overflow-hidden text-ellipsis break-words text-center text-malou-text">
                            {{ diagnostic.address }}
                        </span>
                    </div>
                    <div
                        class="malou-text-70--bold"
                        [ngClass]="{
                            'text-malou-pink': diagnostic.totalScoreRating === DiagnosticRating.BAD,
                            'text-malou-yellow': diagnostic.totalScoreRating === DiagnosticRating.AVERAGE,
                            'text-malou-green': diagnostic.totalScoreRating === DiagnosticRating.GOOD,
                        }">
                        {{ diagnostic.totalScore }}
                    </div>
                </div>
            </div>
        }
        <img class="h-96 w-[360px]" inline="true" [src]="top3DiagnosticsImages()[0] | imagePathResolver: { folder: 'ranking' }" />
    </div>
</ng-template>

<ng-template let-image="image" let-rank="rank" let-diagnostic="diagnostic" #secondOrThirdTemplate>
    <div
        class="absolute top-[24%]"
        [ngClass]="{
            'mr-[480px]': rank === 2,
            'ml-[550px]': rank === 3,
            'fade-in-animation': image === DiagnosticTop3RanksImages.NEW_SECOND || image === DiagnosticTop3RanksImages.NEW_THIRD,
        }">
        <div class="relative">
            @if (diagnostic) {
                <div class="absolute top-12 flex h-full w-full items-center justify-center px-7.5">
                    <div class="flex h-[240px] flex-col items-center justify-center gap-1">
                        <div class="flex flex-col items-center gap-3">
                            <span
                                class="malou-text-15--semibold line-clamp-2 w-[230px] text-ellipsis break-words text-center text-malou-text-title">
                                {{ diagnostic.restaurantName }}
                            </span>
                            <span
                                class="malou-text-12--semibold line-clamp-2 w-[230px] overflow-hidden text-ellipsis break-words text-center text-malou-text">
                                {{ diagnostic.address }}
                            </span>
                        </div>
                        <div
                            class="malou-text-60--bold"
                            [ngClass]="{
                                'text-malou-pink': diagnostic.totalScoreRating === DiagnosticRating.BAD,
                                'text-malou-yellow': diagnostic.totalScoreRating === DiagnosticRating.AVERAGE,
                                'text-malou-green': diagnostic.totalScoreRating === DiagnosticRating.GOOD,
                            }">
                            {{ diagnostic.totalScore }}
                        </div>
                    </div>
                </div>
            }
            <img class="h-[280px] w-[270px]" inline="true" [alt]="image" [src]="image | imagePathResolver: { folder: 'ranking' }" />
        </div>
    </div>
</ng-template>

<ng-template let-diagnostic="diagnostic" #diagnosticScoreTemplate>
    <div
        class="flex flex-row items-center justify-between gap-x-4 rounded-lg border border-malou-primary bg-white px-4 text-center"
        @shiftAnimation
        [ngClass]="{
            hidden: diagnostic.isIgnored,
        }"
        [id]="diagnostic.id">
        <div class="flex w-2/5 flex-row items-center gap-5">
            <div class="w-[7%]">
                <div class="malou-text-18--bold flex h-10 w-10 items-center justify-center rounded-full bg-malou-dark pr-[1px] pt-[3px]">
                    {{ diagnostic.rank }}
                </div>
            </div>
            <div class="flex w-[95%] flex-col items-start text-start">
                <span class="malou-text-18--semibold w-[99%] truncate text-ellipsis text-malou-text-title">
                    {{ diagnostic.restaurantName }}
                </span>
                <span class="malou-text-12--semibold text-malou-text">
                    {{ diagnostic.address }}
                </span>
            </div>
        </div>
        <div class="flex w-3/5 flex-row items-center justify-end">
            <div class="w-2/5">
                <app-loader-progress
                    [showPercentage]="false"
                    [diagnosticRating]="diagnostic.totalScoreRating"
                    [progress]="diagnostic.totalScore"></app-loader-progress>
            </div>

            <div
                class="malou-text-37--bold w-1/5 pt-1"
                [ngClass]="{
                    'text-malou-pink': diagnostic.totalScoreRating === DiagnosticRating.BAD,
                    'text-malou-yellow': diagnostic.totalScoreRating === DiagnosticRating.AVERAGE,
                    'text-malou-green': diagnostic.totalScoreRating === DiagnosticRating.GOOD,
                }">
                {{ diagnostic.totalScore }}
            </div>

            <div class="flex w-1/5 justify-center">
                <app-rating-chip class="w-fit" [diagnosticRating]="diagnostic.totalScoreRating"></app-rating-chip>
            </div>
        </div>
    </div>
</ng-template>
