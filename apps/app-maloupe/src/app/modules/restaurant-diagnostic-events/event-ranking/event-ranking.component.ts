import { NgClass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, OnInit, signal, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import sortedIndexBy from 'lodash.sortedindexby';
import { switchMap, timer } from 'rxjs';

import { DiagnosticRating, MaloupeEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import { RatingChipComponent } from ':modules/rating-chip/rating-chip.component';
import { shiftAnimation } from ':shared/animations';
import { LoaderProgressComponent } from ':shared/components/loader-progress/loader-progress.component';
import { RestaurantRankModalComponent } from ':shared/components/restaurant-rank-modal/restaurant-rank-modal.component';
import {
    DELAY_TO_ADD_DIAGNOSTIC,
    DELAY_TO_REMOVE_NEW_3_RANKS_IMAGES,
    DELAY_TO_SCROLL_BACK_TO_TOP,
    FETCH_DIAGNOSTICS_INTERVAL,
    MAX_DIAGNOSTICS_TO_DISPLAY,
    TREAT_DIAGNOSTICS_INTERVAL,
} from ':shared/constants';
import { Icon, SvgIcon } from ':shared/enums';
import { Diagnostic } from ':shared/models';
import { ImagePathResolverPipe } from ':shared/pipes';

enum DiagnosticTop3Ranks {
    FIRST = 0,
    SECOND = 1,
    THIRD = 2,
}

enum DiagnosticTop3RanksImages {
    FIRST = 'first',
    SECOND = 'second',
    THIRD = 'third',
    NEW_FIRST = 'new_first',
    NEW_SECOND = 'new_second',
    NEW_THIRD = 'new_third',
}

@Component({
    selector: 'app-event-ranking',
    imports: [
        NgClass,
        ImagePathResolverPipe,
        TranslateModule,
        MatIconModule,
        NgTemplateOutlet,
        RatingChipComponent,
        LoaderProgressComponent,
    ],
    templateUrl: './event-ranking.component.html',
    styleUrl: './event-ranking.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [shiftAnimation],
})
export class EventRankingComponent implements OnInit {
    readonly eventName = input.required<MaloupeEventName>();

    private readonly _diagnosticsHttpService = inject(DiagnosticHttpService);
    private readonly _dialog = inject(MatDialog);

    readonly Icon = Icon;
    readonly DiagnosticRating = DiagnosticRating;
    readonly DiagnosticTop3RanksImages = DiagnosticTop3RanksImages;
    readonly SvgIcon = SvgIcon;
    readonly IGNORE_FIRST_DIAGNOSTICS = 3;

    readonly diagnostics: WritableSignal<Diagnostic[]> = signal([]);
    readonly diagnosticsToTreat: WritableSignal<Diagnostic[]> = signal([]);
    readonly diagnosticsToTreatIds = computed(() => this.diagnosticsToTreat().map((diagnostic) => diagnostic.id));
    readonly diagnosticsIds = computed(() => this.diagnostics().map((diagnostic) => diagnostic.id));

    readonly diagnosticsDataToDisplay = computed(() => {
        const diagnostics = this.diagnostics();
        return diagnostics.map((diagnostic, index) => ({
            id: diagnostic.id,
            restaurantName: diagnostic.restaurant.name,
            totalScore: diagnostic.getTotalScore(),
            totalScoreRating: diagnostic.getTotalScoreRating(),
            address: diagnostic.restaurant.address.formattedAddress ?? '',
            rank: index + 1,
            isIgnored: index + 1 <= this.IGNORE_FIRST_DIAGNOSTICS,
        }));
    });

    readonly mappedEventName = computed(() => {
        switch (this.eventName()) {
            case MaloupeEventName.FHT_2025:
                return 'FHT';
            case MaloupeEventName.NRA_2025:
                return 'NRA';
            default:
                return this.eventName();
        }
    });
    readonly top3Diagnostics = computed(() => this.diagnosticsDataToDisplay().filter((diagnostic) => diagnostic.isIgnored));

    readonly top3DiagnosticsImages: WritableSignal<{
        [DiagnosticTop3Ranks.FIRST]: DiagnosticTop3RanksImages;
        [DiagnosticTop3Ranks.SECOND]: DiagnosticTop3RanksImages;
        [DiagnosticTop3Ranks.THIRD]: DiagnosticTop3RanksImages;
    }> = signal({
        [DiagnosticTop3Ranks.FIRST]: DiagnosticTop3RanksImages.FIRST,
        [DiagnosticTop3Ranks.SECOND]: DiagnosticTop3RanksImages.SECOND,
        [DiagnosticTop3Ranks.THIRD]: DiagnosticTop3RanksImages.THIRD,
    });

    ngOnInit(): void {
        this._fetchDiagnostics();
        this._initDiagnosticWatcher();
        this._initDiagnosticTreatmentWatcher();
    }

    private _fetchDiagnostics(): void {
        this._diagnosticsHttpService.getEventDiagnostics$(this.eventName()).subscribe({
            next: (diagnostics) => {
                const sortedDiagnostics = diagnostics
                    .map((diagnostic) => new Diagnostic(diagnostic))
                    .sort((a, b) => b.getTotalScore() - a.getTotalScore());
                this.diagnostics.set(sortedDiagnostics.slice(0, MAX_DIAGNOSTICS_TO_DISPLAY));
            },
        });
    }

    private _initDiagnosticWatcher(): void {
        timer(0, FETCH_DIAGNOSTICS_INTERVAL)
            .pipe(switchMap(() => this._diagnosticsHttpService.getEventDiagnostics$(this.eventName())))
            .subscribe((diagnostics) => {
                const currentDiagnosticIds = this.diagnosticsIds();
                const newDiagnostics = diagnostics
                    .filter(
                        (diagnostic) =>
                            !currentDiagnosticIds.includes(diagnostic.id) && !this.diagnosticsToTreatIds().includes(diagnostic.id)
                    )
                    .map((diagnostic) => new Diagnostic(diagnostic));
                if (newDiagnostics.length !== 0) {
                    this.diagnosticsToTreat.set([...this.diagnosticsToTreat(), ...newDiagnostics]);
                }
            });
    }

    private _initDiagnosticTreatmentWatcher(): void {
        timer(0, TREAT_DIAGNOSTICS_INTERVAL).subscribe(() => {
            if (this.diagnosticsToTreat().length > 0) {
                const diagnostic = this.diagnosticsToTreat()[0];
                this._showNewDiagnosticPopin(diagnostic);
            }
        });
    }

    private _showNewDiagnosticPopin(diagnostic: Diagnostic): void {
        const index = this._getDiagnosticRank(diagnostic);
        this._dialog
            .open(RestaurantRankModalComponent, {
                data: {
                    restaurantName: diagnostic.restaurant.name,
                    restaurantAddress: diagnostic.restaurant.address.formattedAddress ?? '',
                    totalScore: diagnostic.getTotalScore(),
                    totalScoreRating: diagnostic.getTotalScoreRating(),
                    rank: index + 1,
                },
                height: '650px',
                width: '900px',
                panelClass: 'malou-dialog-panel',
            })
            .afterClosed()
            .subscribe(() => {
                setTimeout(() => {
                    this._addItem(diagnostic);
                    this.diagnosticsToTreat.set([...this.diagnosticsToTreat().slice(1)]);
                }, DELAY_TO_ADD_DIAGNOSTIC);
            });
    }

    private _getDiagnosticRank(diagnosticToAdd: Diagnostic): number {
        const currentDiagnostics = this.diagnostics();
        const currentDiagnosticsScores = currentDiagnostics.map((diagnostic) => diagnostic.getTotalScore());
        return sortedIndexBy(currentDiagnosticsScores, diagnosticToAdd.getTotalScore(), (score) => -score);
    }

    private _addItem(diagnosticToAdd: Diagnostic): void {
        const currentDiagnostics = this.diagnostics();
        const index = this._getDiagnosticRank(diagnosticToAdd);
        currentDiagnostics.splice(index, 0, diagnosticToAdd);
        this.diagnostics.set([...currentDiagnostics]);
        if (index < this.IGNORE_FIRST_DIAGNOSTICS) {
            this._setImageForTop3Diagnostics(index);
            setTimeout(() => this._scrollToTop());
        } else {
            setTimeout(() => this._scrollToDiagnostic(diagnosticToAdd.id), 0);
        }
    }

    private _setImageForTop3Diagnostics(index: number): void {
        this.top3DiagnosticsImages.set({
            ...this.top3DiagnosticsImages(),
            [index]: this._mapRankToImage(index, true),
        });
        setTimeout(() => {
            this.top3DiagnosticsImages.set({
                ...this.top3DiagnosticsImages(),
                [index]: this._mapRankToImage(index, false),
            });
        }, DELAY_TO_REMOVE_NEW_3_RANKS_IMAGES);
    }
    private _scrollToTop(): void {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    private _scrollToDiagnostic(id: string) {
        const diagnostic = document.getElementById(id);
        if (diagnostic) {
            diagnostic.scrollIntoView({ behavior: 'smooth', block: 'center' });
            setTimeout(() => {
                this._scrollToTop();
            }, DELAY_TO_SCROLL_BACK_TO_TOP);
        }
    }

    private _mapRankToImage(rank: number, isNew: boolean): DiagnosticTop3RanksImages {
        switch (rank) {
            case 0:
                return isNew ? DiagnosticTop3RanksImages.NEW_FIRST : DiagnosticTop3RanksImages.FIRST;
            case 1:
                return isNew ? DiagnosticTop3RanksImages.NEW_SECOND : DiagnosticTop3RanksImages.SECOND;
            case 2:
                return isNew ? DiagnosticTop3RanksImages.NEW_THIRD : DiagnosticTop3RanksImages.THIRD;
            default:
                return DiagnosticTop3RanksImages.FIRST;
        }
    }
}
