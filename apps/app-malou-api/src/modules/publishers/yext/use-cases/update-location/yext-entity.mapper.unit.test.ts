import { YextEntityMapper } from ':modules/publishers/yext/use-cases/update-location/yext-entity.mapper';

describe('YextEntityMapper', () => {
    describe('mapRestaurantHoursToYextEntityHolidayHours', () => {
        it('should merge openIntervals and format holiday hours correctly', async () => {
            const specialHours = [
                {
                    name: "Période d'horaires exceptionnels",
                    startDate: {
                        day: 8,
                        year: 2025,
                        month: 5,
                    },
                    openTime: '14:30',
                    endDate: {
                        day: 8,
                        year: 2025,
                        month: 5,
                    },
                    closeTime: '21:00',
                    isClosed: false,
                    isFromCalendarEvent: false,
                },
                {
                    name: '🎇 Fête nationale',
                    startDate: {
                        day: 14,
                        year: 2025,
                        month: 6,
                    },
                    openTime: '11:30',
                    endDate: {
                        day: 14,
                        year: 2025,
                        month: 6,
                    },
                    closeTime: '14:30',
                    isClosed: false,
                    isFromCalendarEvent: true,
                },
                {
                    name: '🎉 <PERSON><PERSON>',
                    startDate: {
                        day: 9,
                        year: 2025,
                        month: 5,
                    },
                    openTime: '11:30',
                    endDate: {
                        day: 9,
                        year: 2025,
                        month: 5,
                    },
                    closeTime: '14:30',
                    isClosed: false,
                    isFromCalendarEvent: true,
                },
                {
                    name: '🪻 Pentecôte',
                    startDate: {
                        day: 8,
                        year: 2025,
                        month: 5,
                    },
                    openTime: '11:00',
                    endDate: {
                        day: 8,
                        year: 2025,
                        month: 5,
                    },
                    closeTime: '14:30',
                    isClosed: false,
                    isFromCalendarEvent: true,
                },
                {
                    name: '🎉 Ascension',
                    startDate: {
                        day: 29,
                        year: 2025,
                        month: 4,
                    },
                    openTime: '11:00',
                    endDate: {
                        day: 29,
                        year: 2025,
                        month: 4,
                    },
                    closeTime: '14:30',
                    isClosed: false,
                    isFromCalendarEvent: true,
                },
                {
                    name: '🎉 Ascension',
                    startDate: {
                        day: 29,
                        year: 2025,
                        month: 4,
                    },
                    openTime: '18:00',
                    endDate: {
                        day: 29,
                        year: 2025,
                        month: 4,
                    },
                    closeTime: '23:00',
                    isClosed: false,
                    isFromCalendarEvent: true,
                },
            ];
            const expectedResult = [
                {
                    date: '2025-06-08',
                    isClosed: false,
                    openIntervals: [
                        {
                            start: '11:00',
                            end: '21:00',
                        },
                    ],
                },
                {
                    date: '2025-07-14',
                    isClosed: false,
                    openIntervals: [
                        {
                            start: '11:30',
                            end: '14:30',
                        },
                    ],
                },
                {
                    date: '2025-06-09',
                    isClosed: false,
                    openIntervals: [
                        {
                            start: '11:30',
                            end: '14:30',
                        },
                    ],
                },
                {
                    date: '2025-05-29',
                    isClosed: false,
                    openIntervals: [
                        {
                            start: '11:00',
                            end: '14:30',
                        },
                        {
                            start: '18:00',
                            end: '23:00',
                        },
                    ],
                },
            ];

            const mapper: YextEntityMapper = Object.create(YextEntityMapper.prototype);
            const result = mapper.mapRestaurantHoursToYextEntityHolidayHours(specialHours);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('mergeOverlappingIntervals', () => {
        it('should handle overlapping hours', async () => {
            const openIntervals = [
                { start: '11:00', end: '14:30' },
                { start: '21:00', end: '23:00' },
                { start: '22:00', end: '23:30' },
                { start: '20:00', end: '22:30' },
            ];

            const expectedResult = [
                { start: '11:00', end: '14:30' },
                { start: '20:00', end: '23:30' },
            ];

            const mapper: YextEntityMapper = Object.create(YextEntityMapper.prototype);
            const result = mapper.mergeOverlappingIntervals(openIntervals);

            expect(result).toEqual(expectedResult);
        });

        it('should handle overlapping hours', async () => {
            const openIntervals = [
                { start: '11:00', end: '17:30' },
                { start: '14:00', end: '16:30' },
                { start: '22:00', end: '23:30' },
                { start: '22:00', end: '22:30' },
            ];

            const expectedResult = [
                { start: '11:00', end: '17:30' },
                { start: '22:00', end: '23:30' },
            ];

            const mapper: YextEntityMapper = Object.create(YextEntityMapper.prototype);
            const result = mapper.mergeOverlappingIntervals(openIntervals);

            expect(result).toEqual(expectedResult);
        });

        it('should handle overlapping hours', async () => {
            const openIntervals = [
                { start: '11:00', end: '14:30' },
                { start: '13:00', end: '17:00' },
                { start: '16:30', end: '19:30' },
                { start: '20:00', end: '22:30' },
            ];

            const expectedResult = [
                { start: '11:00', end: '19:30' },
                { start: '20:00', end: '22:30' },
            ];

            const mapper: YextEntityMapper = Object.create(YextEntityMapper.prototype);
            const result = mapper.mergeOverlappingIntervals(openIntervals);

            expect(result).toEqual(expectedResult);
        });
    });
});
