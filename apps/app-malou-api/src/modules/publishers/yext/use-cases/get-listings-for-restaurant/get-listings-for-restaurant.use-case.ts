import { singleton } from 'tsyringe';

import { ListingDto } from '@malou-io/package-dto';
import { getPlatformKeysWithYextPublisherIds, YextPublisherId } from '@malou-io/package-utils';

import YextListingService from ':modules/publishers/yext/services/yext-listing.service';
import { ListingsDtoMapper } from ':modules/publishers/yext/use-cases/get-listings-for-restaurant/listings.dto-mapper';

@singleton()
export default class GetListingsForRestaurantUseCase {
    constructor(
        private readonly _yextListingService: YextListingService,
        private readonly _listingsMapperDto: ListingsDtoMapper
    ) {}

    async execute(restaurantId: string): Promise<ListingDto[]> {
        const list = await this._yextListingService.getListingsForRestaurant(restaurantId);

        // We filter out Apple because we want it to be considered as a Yext only publisher
        const publishersHandledByMalou = getPlatformKeysWithYextPublisherIds().filter(
            (publisherId) => publisherId !== YextPublisherId.APPLE
        ) as YextPublisherId[];

        return list
            .filter((listing) => !publishersHandledByMalou.includes(listing.id as YextPublisherId))
            .map((listing) => this._listingsMapperDto.toListingDto(listing));
    }
}
