import fs from 'fs';

import { newDbId } from '@malou-io/package-models';
import { FileFormat, MalouErrorCode, MediaType, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Media } from ':modules/media/entities/media.entity';
import { ImageResizer } from ':modules/media/services/image-resizers/image-resizer.port';
import {
    CropMode,
    CropOptions,
    FileMetadata,
    ImageResizerPort,
    OutputFileInfo,
    ResizeOptions,
    VideoResizerCropOptions,
} from ':modules/media/services/image-resizers/image-resizer.port.interface';
import { VideoResizer } from ':modules/media/services/video-resizers/video-resizer.port';
import { VideoResizerPort } from ':modules/media/services/video-resizers/video-resizer.port.interface';

import { SizeConstraints } from '../media-formatter.port.interface';
import { FbMediaFormatter } from './fb-media-formatter.adapter';

jest.mock('fs');

class MockImageResizerAdapter implements ImageResizerPort {
    resize(_options: ResizeOptions): ImageResizerPort {
        return this;
    }
    extract(_options: CropOptions): ImageResizerPort {
        return this;
    }
    toFile(_outputPath: string): Promise<OutputFileInfo> {
        return null as any;
    }
    initialize(_filePath: string, _outputPath: string): ImageResizerPort {
        return this;
    }
    rotate(_degrees?: number): ImageResizerPort {
        return this;
    }
    async metadata(): Promise<FileMetadata> {
        return {
            width: 1920,
            height: 1080,
            size: 2000000,
            format: FileFormat.JPG,
            url: 'url',
            pathWhereFileIsStored: 'pathWhereFileIsStored',
            mimetype: 'mimetype',
            name: 'name',
            type: MediaType.PHOTO,
        };
    }
    getOutputPath(): string {
        return null as any;
    }
}

class MockVideoResizerAdapter implements VideoResizerPort {
    upload(_filePath: string, _outputFilePath: string, _options: CropOptions): Promise<FileMetadata> {
        logger.info('called upload stub method');
        return null as any;
    }
}

type TestCase = {
    media: Media;
    testedOn: string;
    expectedResult: {
        inputPath: string;
        outputPath: string;
        expectedInputPath: string;
        expectedOutputPath: string;
        expectedSizeRecommendation: {
            forInstagram: SizeConstraints;
            forFacebook: SizeConstraints;
        };
        expectedResizeOptions: ResizeOptions | VideoResizerCropOptions;
    };
};

const dbId = newDbId().toString();
const testCases: TestCase[] = [
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1920,
                    height: 1080,
                },
            },
            urls: {
                original: 'https://www.facebook.com/1.jpg',
            },
            type: MediaType.PHOTO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 1.77,
                width: 1920,
                height: 1080,
            },
            format: FileFormat.JPG,
        } as any),
        testedOn: 'photo with no modification in landscape format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.jpg`,
            outputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedInputPath: `downloadedMedias/${dbId}.jpg`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 340,
                    maxHeight: 610,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 340,
                    maxHeight: 610,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1920,
                height: 1080,
                fit: 'cover',
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1080,
                    height: 1920,
                },
            },
            urls: {
                original: 'https://www.facebook.com/2.jpg',
            },
            type: MediaType.PHOTO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 0.56,
                width: 1080,
                height: 1920,
            },
            format: FileFormat.JPG,
        } as any),
        testedOn: 'photo with no modification in portrait format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.jpg`,
            outputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedInputPath: `downloadedMedias/${dbId}.jpg`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1920,
                fit: 'cover',
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1080,
                    height: 1080,
                },
            },
            urls: {
                original: 'https://www.facebook.com/3.jpg',
            },
            type: MediaType.PHOTO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 1,
                width: 1080,
                height: 1080,
            },
            format: FileFormat.JPG,
        } as any),
        testedOn: 'photo with no modification in square format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.jpg`,
            outputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedInputPath: `downloadedMedias/${dbId}.jpg`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.jpg`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 600,
                    maxHeight: 1080,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 600,
                    maxHeight: 1080,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1080,
                fit: 'cover',
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1920,
                    height: 1080,
                },
            },
            urls: {
                original: 'https://www.facebook.com/4.mp4',
            },
            type: MediaType.VIDEO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 1.77,
                width: 1920,
                height: 1080,
            },
            format: FileFormat.MP4,
        } as any),
        testedOn: 'video with no modification in landscape format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.mp4`,
            outputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedInputPath: `downloadedMedias/${dbId}.mp4`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 340,
                    maxHeight: 610,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 340,
                    maxHeight: 610,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 610,
                cropMode: CropMode.SCALE,
                aspect_ratio: '16:9',
                originalMedia: {
                    width: 1920,
                    height: 1080,
                    maxDimension: {
                        width: 1080,
                        height: 610,
                    },
                },
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1080,
                    height: 1920,
                },
            },
            urls: {
                original: 'https://www.facebook.com/5.mp4',
            },
            type: MediaType.VIDEO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 0.56,
                width: 1080,
                height: 1920,
            },
            format: FileFormat.MP4,
        } as any),
        testedOn: 'video with no modification in portrait format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.mp4`,
            outputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedInputPath: `downloadedMedias/${dbId}.mp4`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1920,
                cropMode: CropMode.SCALE,
                aspect_ratio: '9:16',
                originalMedia: {
                    width: 1080,
                    height: 1920,
                    maxDimension: {
                        width: 1080,
                        height: 1920,
                    },
                },
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 1080,
                    height: 1080,
                },
            },
            urls: {
                original: 'https://www.facebook.com/6.mp4',
            },
            type: MediaType.VIDEO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 1,
                width: 1080,
                height: 1080,
            },
            format: FileFormat.MP4,
        } as any),
        testedOn: 'video with no modification in square format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.mp4`,
            outputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedInputPath: `downloadedMedias/${dbId}.mp4`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 600,
                    maxHeight: 1080,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 600,
                    maxHeight: 1080,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1080,
                cropMode: CropMode.SCALE,
                aspect_ratio: '1:1',
                originalMedia: {
                    width: 1080,
                    height: 1080,
                    maxDimension: {
                        width: 1080,
                        height: 1080,
                    },
                },
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 2160,
                    height: 3840,
                },
            },
            urls: {
                original: 'https://www.facebook.com/7.mp4',
            },
            type: MediaType.VIDEO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 0.56,
                width: 1080,
                height: 1920,
            },
            format: FileFormat.MP4,
        } as any),
        testedOn: 'larger video with no modification in portrait format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.mp4`,
            outputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedInputPath: `downloadedMedias/${dbId}.mp4`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1920,
                cropMode: CropMode.SCALE,
                aspect_ratio: '9:16',
                originalMedia: {
                    width: 2160,
                    height: 3840,
                    maxDimension: {
                        width: 1080,
                        height: 1920,
                    },
                },
            },
        },
    },
    {
        media: new Media({
            id: dbId,
            sizes: {
                original: 2000000,
            },
            dimensions: {
                original: {
                    width: 2160,
                    height: 3840,
                },
            },
            urls: {
                original: 'https://www.facebook.com/8.mp4',
            },
            type: MediaType.VIDEO,
            resizeMetadata: {
                cropPosition: {
                    left: 0,
                    top: 0,
                },
                aspectRatio: 0.8,
                width: 1080,
                height: 1350,
            },
            format: FileFormat.MP4,
        } as any),
        testedOn: 'larger video with different aspect ratio in portrait format',
        expectedResult: {
            inputPath: `downloadedMedias/${dbId}.mp4`,
            outputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedInputPath: `downloadedMedias/${dbId}.mp4`,
            expectedOutputPath: `downloadedMedias/${dbId}_resized.mp4`,
            expectedSizeRecommendation: {
                forFacebook: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
                forInstagram: {
                    minWidth: 600,
                    minHeight: 1067,
                    maxHeight: 1920,
                    maxWidth: 1080,
                },
            },
            expectedResizeOptions: {
                width: 1080,
                height: 1350,
                cropMode: CropMode.CROP,
                aspect_ratio: '4:5',
                originalMedia: {
                    width: 2160,
                    height: 3840,
                    maxDimension: { width: 1080, height: 1920 },
                },
            },
        },
    },
];
describe.each(testCases)('FbMediaFormatter', ({ media, testedOn, expectedResult }) => {
    let fbMediaFormatter: FbMediaFormatter;
    let imageResizerAdapter: ImageResizerPort;
    let videoResizerAdapter: VideoResizerPort;
    let imageResizer: ImageResizer;
    let videoResizer: VideoResizer;

    beforeEach(() => {
        imageResizerAdapter = new MockImageResizerAdapter();
        videoResizerAdapter = new MockVideoResizerAdapter();
        imageResizer = new ImageResizer(imageResizerAdapter);
        videoResizer = new VideoResizer(videoResizerAdapter);
        fbMediaFormatter = new FbMediaFormatter(media, imageResizer, videoResizer);
    });

    describe(`formatMedia : ${testedOn}`, () => {
        it('should format photo if mediaType is photo', async () => {
            // stub fs
            jest.spyOn(fs, 'unlinkSync').mockImplementation(() => null);

            const formatImageSpy = jest.spyOn(fbMediaFormatter, 'formatImage');
            const inputPath = 'inputPath';
            const outputPath = 'outputPath';
            const platform = PlatformKey.FACEBOOK;
            const mediaType = MediaType.PHOTO;
            const resizeParams = null as any;
            const options = undefined;

            await fbMediaFormatter.formatMedia(inputPath, outputPath, platform, mediaType, resizeParams, options);

            expect(formatImageSpy).toHaveBeenCalledWith(inputPath, outputPath, platform, resizeParams, options);
        });

        it('should format video if mediaType is video', async () => {
            const formatVideoSpy = jest.spyOn(fbMediaFormatter, 'formatVideo');
            const inputPath = 'inputPath';
            const outputPath = 'outputPath';
            const platform = PlatformKey.FACEBOOK;
            const mediaType = MediaType.VIDEO;
            const resizeParams = null as any;
            const options = undefined;

            await fbMediaFormatter.formatMedia(inputPath, outputPath, platform, mediaType, resizeParams, options);

            expect(formatVideoSpy).toHaveBeenCalledWith(inputPath, outputPath, platform, options);
        });

        it('should throw error if mediaType is not photo or video', async () => {
            const inputPath = 'inputPath';
            const outputPath = 'outputPath';
            const platform = PlatformKey.FACEBOOK;
            const mediaType = MediaType.FILE;
            const resizeParams = null as any;

            await expect(fbMediaFormatter.formatMedia(inputPath, outputPath, platform, mediaType, resizeParams)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.WRONG_MEDIA_TYPE,
                })
            );
        });
    });

    describe(`formatVideo : ${testedOn}`, () => {
        it('should call _videoResizer.upload with correct parameters', async () => {
            if (media.type !== MediaType.VIDEO) {
                expect(true).toBe(true);
                return;
            }
            const uploadSpy = jest.spyOn(videoResizer, 'upload');
            const inputFilePath = `downloadedMedias/${media.id}.${media.urls.original.split('.com/')[1].split('.')[1]}`;
            const outputFilePath = inputFilePath.replace(
                `.${media.getOriginalImageExtension()}`,
                `_resized.${media.getOriginalImageExtension()}`
            );
            const platform = PlatformKey.FACEBOOK;

            await fbMediaFormatter.formatVideo(inputFilePath, outputFilePath, platform);

            expect(uploadSpy).toHaveBeenCalledWith(
                expectedResult.inputPath,
                expectedResult.outputPath,
                expect.objectContaining(expectedResult.expectedResizeOptions)
            );
        });
    });

    describe(`formatImage : ${testedOn}`, () => {
        it('should call _imageResizer.initialize with inputPath and outputPath', async () => {
            const initializeSpy = jest.spyOn(imageResizer, 'initialize');
            const inputFilePath = `downloadedMedias/${media.id}.${media.urls.original.split('.com/')[1].split('.')[1]}`;
            const outputFilePath = inputFilePath.replace(
                `.${media.getOriginalImageExtension()}`,
                `_resized.${media.getOriginalImageExtension()}`
            );
            const platform = PlatformKey.FACEBOOK;
            await fbMediaFormatter.formatImage(inputFilePath, outputFilePath, platform, media.getCropOptions());

            expect(initializeSpy).toHaveBeenCalledWith(expectedResult.expectedInputPath, expectedResult.expectedOutputPath);
        });

        it('should resize image to fit within size recommendation if necessary', async () => {
            const resizeSpy = jest.spyOn(imageResizer, 'resize');
            const inputFilePath = `downloadedMedias/${media.id}.${media.urls.original.split('.com/')[1].split('.')[1]}`;
            const outputFilePath = inputFilePath.replace(
                `.${media.getOriginalImageExtension()}`,
                `_resized.${media.getOriginalImageExtension()}`
            );
            const platform = PlatformKey.FACEBOOK;

            await fbMediaFormatter.formatImage(inputFilePath, outputFilePath, platform, media.getCropOptions());
            const haveBeenCalledTimes = resizeSpy.mock.calls.length;
            expect(resizeSpy).toHaveBeenCalledTimes(haveBeenCalledTimes);
            if (haveBeenCalledTimes > 0) {
                expect(resizeSpy).toHaveBeenCalledWith(expectedResult.expectedResizeOptions);
            }
        });
    });
});
