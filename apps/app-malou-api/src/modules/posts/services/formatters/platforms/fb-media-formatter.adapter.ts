import assert from 'assert';
import fs from 'fs';
import { capitalize } from 'lodash';
import { autoInjectable } from 'tsyringe';

import { MalouErrorCode, MediaType, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Media } from ':modules/media/entities/media.entity';
import { ImageResizer } from ':modules/media/services/image-resizers/image-resizer.port';
import { CropMode, CropOptions, FileMetadata, ImageResizerPort } from ':modules/media/services/image-resizers/image-resizer.port.interface';
import { VideoResizer } from ':modules/media/services/video-resizers/video-resizer.port';
import { MediaFormatterPort, SizeConstraints } from ':modules/posts/services/formatters/media-formatter.port.interface';

import { AspectRatio, googleSizeRecommendation, instagramSizeRecommendation } from './constants';

@autoInjectable()
export class FbMediaFormatter implements MediaFormatterPort {
    media: Media;
    constructor(
        media: Media,
        private readonly _imageResizer?: ImageResizer,
        private readonly _videoResizer?: VideoResizer
    ) {
        this.media = media;
    }

    async formatMedia(
        inputPath: string,
        outputPath: string,
        platform: PlatformKey,
        mediaType: MediaType,
        resizeParams: CropOptions,
        options?: {
            shouldForceResizeToRecommendedSize?: boolean;
        }
    ): Promise<FileMetadata> {
        logger.info('[FbMediaFormatter] formatMedia: start', { inputPath, outputPath, platform, mediaType, resizeParams, options });
        if (mediaType === MediaType.PHOTO) {
            return this.formatImage(inputPath, outputPath, platform, resizeParams, options);
        } else if (mediaType === MediaType.VIDEO) {
            return this.formatVideo(inputPath, outputPath, platform, options);
        } else {
            throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                metadata: {
                    platform,
                    mediaType,
                },
            });
        }
    }

    async formatVideo(
        url: string,
        outputPath: string,
        _platform: PlatformKey,
        options?: {
            shouldForceResizeToRecommendedSize?: boolean;
        }
    ): Promise<FileMetadata> {
        assert(this._videoResizer);
        assert(this.media.resizeMetadata, '[FbMediaFormatter] media must have resizeMetadata');
        assert(this.media.dimensions, '[FbMediaFormatter] media must have dimensions');
        assert(this.media.dimensions.original, '[FbMediaFormatter] media must have dimensions.original');
        assert(this.media.resizeMetadata.aspectRatio, '[FbMediaFormatter] media must have resizeMetadata.aspectRatio');
        const originalMediaAspectRatio = parseFloat(
            (this.media.dimensions.original.width / this.media.dimensions.original.height).toFixed(4)
        );
        const originalMediaMaxDimension = this.getDimensionRecommendationForPlatform({
            aspectRatio: originalMediaAspectRatio,
        });
        const resizeToAspectRatio = parseFloat(this.media.resizeMetadata.aspectRatio?.toFixed(4));
        const sizeToFill = this.media.resizeMetadata?.aspectRatio > 1 ? 'width' : 'height';
        const otherSize = sizeToFill === 'width' ? 'height' : 'width';
        const sizeRecommendation = this.getDimensionRecommendationForPlatform({
            shouldForceResizeToRecommendedSize: options?.shouldForceResizeToRecommendedSize,
            aspectRatio: resizeToAspectRatio,
        });
        const mainMaxOrMinSize = this.getMainMaxOrMinSize(sizeToFill, sizeRecommendation);
        const secondaryMaxOrMinSize = this.getSecondaryMaxOrMinSize(mainMaxOrMinSize, sizeToFill, otherSize);
        const aspect_ratio =
            this.getAspectRatioName(resizeToAspectRatio) === 'too_big' ? '16:9' : this.getAspectRatioName(resizeToAspectRatio);

        const mainRecommendationSize = sizeRecommendation[mainMaxOrMinSize] ?? this.media.dimensions.original[sizeToFill];
        const secondRecommendationSize = sizeRecommendation[secondaryMaxOrMinSize] ?? this.media.dimensions.original[otherSize];
        const targetAspectRatio =
            sizeToFill === 'width' ? mainRecommendationSize / secondRecommendationSize : secondRecommendationSize / mainRecommendationSize;
        const cropMode = this._getCropMode(mainRecommendationSize, sizeToFill, targetAspectRatio);
        const otherOptions = {};
        if (cropMode === 'pad') {
            otherOptions['background'] = '#000000';
        }

        return this._videoResizer.upload(url, outputPath, {
            cropMode,
            aspect_ratio: aspect_ratio,
            [sizeToFill]: mainRecommendationSize,
            [otherSize]: secondRecommendationSize,
            originalMedia: {
                width: this.media.dimensions.original.width,
                height: this.media.dimensions.original.height,
                maxDimension: {
                    width: originalMediaMaxDimension.maxWidth,
                    height: originalMediaMaxDimension.maxHeight,
                },
            },
            ...otherOptions,
        });
    }

    async formatImage(
        inputPath: string,
        outputPath: string,
        platformKey: PlatformKey,
        resizeParams: CropOptions | null,
        options?: {
            shouldForceResizeToRecommendedSize?: boolean;
        }
    ): Promise<FileMetadata> {
        assert(this._imageResizer);
        assert(this.media.resizeMetadata, '[FbMediaFormatter] media must have resizeMetadata');
        const sizeRecommendation = this.getDimensionRecommendationForPlatform({
            platformKey,
            aspectRatio: parseFloat(this.media.resizeMetadata.aspectRatio.toFixed(4)),
        });
        const initialMedia = this._imageResizer.initialize(inputPath, outputPath);
        let hasPassedThroughExtraction = false;
        let finalMedia = initialMedia;
        const mediaMetadata = await initialMedia.metadata();

        if (options?.shouldForceResizeToRecommendedSize) {
            finalMedia = await this._cropToPortrait(initialMedia, outputPath, inputPath);
            hasPassedThroughExtraction = true;
            return this._deleteLocalFileAndGetMetadata(outputPath, finalMedia, hasPassedThroughExtraction);
        }

        if (resizeParams != null && resizeParams.width != 0 && resizeParams.height != 0) {
            finalMedia = await this._extractCroppedImage(finalMedia, resizeParams, outputPath, inputPath);
            hasPassedThroughExtraction = true;
        }
        const finalMediaMetadata = finalMedia != null ? await finalMedia.metadata() : mediaMetadata;
        // Resize image to fit within the size recommendation
        if (
            finalMediaMetadata.width < sizeRecommendation.minWidth ||
            finalMediaMetadata.height < sizeRecommendation.minHeight ||
            sizeRecommendation.shouldExtractToMinSize
        ) {
            finalMedia.resize({
                width: sizeRecommendation.minWidth,
                height: sizeRecommendation.minHeight,
                fit: 'cover',
            });
            return this._deleteLocalFileAndGetMetadata(outputPath, finalMedia, hasPassedThroughExtraction);
        }

        if (finalMediaMetadata.width > sizeRecommendation.maxWidth || finalMediaMetadata.height > sizeRecommendation.maxHeight) {
            finalMedia.resize({
                width: sizeRecommendation.maxWidth,
                height: sizeRecommendation.maxHeight,
                fit: 'cover',
            });

            return this._deleteLocalFileAndGetMetadata(outputPath, finalMedia, hasPassedThroughExtraction);
        }
        return this._deleteLocalFileAndGetMetadata(outputPath, finalMedia, hasPassedThroughExtraction);
    }

    getDimensionRecommendationForPlatform(options: {
        shouldForceResizeToRecommendedSize?: boolean;
        platformKey?: PlatformKey;
        aspectRatio: number;
    }): SizeConstraints {
        if (options?.shouldForceResizeToRecommendedSize) {
            return instagramSizeRecommendation[AspectRatio.PORTRAIT];
        }
        const aspectRatioName = this.getAspectRatioName(options.aspectRatio);
        // Because we are using the same formatter for social and seo posts, the only problem that we can have is that
        // if an image has an aspect ratio of Cinema, the accepted min width is 168px, which is too small for SEO.
        // so, instead we will use the one from gmb, preserve the aspect ration and bump the width if needed.
        // TODO: create another formatter for SEO posts !!
        if (options?.platformKey === PlatformKey.GMB && aspectRatioName === AspectRatio.CINEMA) {
            return googleSizeRecommendation[AspectRatio.CINEMA];
        }
        return instagramSizeRecommendation[aspectRatioName];
    }

    getAspectRatioName(aspectRatio: number): string {
        if (aspectRatio >= 0.8 && aspectRatio < 1) {
            return '4:5';
        } else if (aspectRatio > 1 && aspectRatio < 1.91) {
            return '16:9';
        } else if (aspectRatio < 0.8) {
            return '9:16';
        } else if (aspectRatio === 1) {
            return '1:1';
        } else if (aspectRatio === 1.91) {
            return '191:100';
        } else {
            return 'too_big';
        }
    }

    getSecondaryMaxOrMinSize(mainMaxOrMinSize: string, sizeToFill: string, otherSize: string) {
        return mainMaxOrMinSize === `max${capitalize(sizeToFill)}`
            ? `max${capitalize(otherSize)}`
            : mainMaxOrMinSize === `min${capitalize(sizeToFill)}`
              ? `min${capitalize(otherSize)}`
              : otherSize;
    }

    getMainMaxOrMinSize(sizeToFill: string, sizeRecommendation: SizeConstraints) {
        assert(this.media.dimensions, '[FbMediaFormatter] media must have dimensions');
        assert(this.media.dimensions.original, '[FbMediaFormatter] media must have dimensions.original');
        const sizes = {
            max: {
                height: {
                    size: sizeRecommendation.maxHeight,
                    name: 'maxHeight',
                },
                width: {
                    size: sizeRecommendation.maxWidth,
                    name: 'maxWidth',
                },
            },
            min: {
                height: {
                    size: sizeRecommendation.minHeight,
                    name: 'minHeight',
                },
                width: {
                    size: sizeRecommendation.minWidth,
                    name: 'minWidth',
                },
            },
        };

        if (this.media.dimensions.original[sizeToFill] >= sizes.max[sizeToFill].size) {
            return sizes.max[sizeToFill].name;
        } else if (this.media.dimensions.original[sizeToFill] <= sizes.min[sizeToFill].size) {
            return sizes.min[sizeToFill].name;
        }
        return sizes.min[sizeToFill].name;
    }

    private async _deleteLocalFileAndGetMetadata(
        outputPath: string,
        finalMedia: ImageResizerPort,
        hasPassedThroughExtraction: boolean
    ): Promise<FileMetadata> {
        assert(this._imageResizer);
        await finalMedia.toFile(
            outputPath.replace(`.${this.media.getOriginalImageExtension()}`, `_fitted.${this.media.getOriginalImageExtension()}`)
        );
        if (hasPassedThroughExtraction) {
            fs.unlinkSync(outputPath);
        }
        finalMedia = this._imageResizer.initialize(finalMedia.getOutputPath(), finalMedia.getOutputPath());
        const outputMetadata = await finalMedia.metadata();
        return outputMetadata;
    }

    private async _extractCroppedImage(finalMedia: ImageResizerPort, cropOptions: CropOptions, outputPath: string, inputPath: string) {
        assert(this._imageResizer);
        let newMedia = finalMedia;
        try {
            newMedia = finalMedia.rotate().extract(cropOptions); // We need to rotate the image by its potential exif orientation before cropping it as the crop params were computed on the rotated image
            await newMedia.toFile(outputPath);
        } catch (error: any) {
            // to troubleshoot Sharp/libvips errors like “bad extract area”:
            logger.error(
                `[FbMediaFormatter] _extractCroppedImage: Error while cropping image: ${JSON.stringify({ stack: error.stack, error, cropOptions, outputPath, inputPath, mediaId: this.media.id })}`
            );
            assert(this.media.dimensions, '[FbMediaFormatter] media must have dimensions');
            assert(this.media.dimensions.original, '[FbMediaFormatter] media must have dimensions.original');

            if (error.toString().includes('bad')) {
                logger.info('[FbMediaFormatter] _extractCroppedImage: workaround ater failure', {
                    mediaId: this.media.id,
                    mediaDimensions: this.media.dimensions,
                });
                newMedia = finalMedia.rotate().extract({
                    left: 0,
                    top: 0,
                    width: this.media.dimensions.original.width,
                    height: this.media.dimensions.original.height,
                });
                await newMedia.toFile(outputPath);
            }
        }
        fs.unlinkSync(inputPath);
        finalMedia = this._imageResizer.initialize(outputPath, outputPath);
        return newMedia;
    }

    private _getCropMode(mainRecommendationSize: number, sizeToFillName: 'height' | 'width', targetAspectRatio: number): CropMode {
        assert(this.media.dimensions, '[FbMediaFormatter] media must have dimensions');
        assert(this.media.dimensions.original, '[FbMediaFormatter] media must have dimensions.original');
        const sizeToFill = this.media.dimensions.original[sizeToFillName];
        const originalVideoRatio = this.media.dimensions.original.width / this.media.dimensions.original.height;

        if (Math.abs(originalVideoRatio - targetAspectRatio) < 0.01) {
            return CropMode.SCALE;
        }

        if (mainRecommendationSize < sizeToFill) {
            return CropMode.CROP;
        }

        return CropMode.PAD;
    }

    private async _cropToPortrait(image: ImageResizerPort, outputPath: string, inputPath: string): Promise<ImageResizerPort> {
        assert(this._imageResizer);
        const recommendedSize = instagramSizeRecommendation[AspectRatio.PORTRAIT];
        const recommendedAspectRatio = recommendedSize.maxWidth / recommendedSize.maxHeight;
        const metadata = await image.metadata();

        assert(metadata.normalWidth, '[FbMediaFormatter] media must have normalWidth');
        assert(metadata.normalHeight, '[FbMediaFormatter] media must have normalHeight');

        let newWidth = metadata.normalWidth;
        let newHeight = metadata.normalHeight;
        const aspectRatio = metadata.normalWidth / metadata.normalHeight;

        if (aspectRatio < recommendedAspectRatio) {
            newHeight = metadata.normalWidth / recommendedAspectRatio;
            newWidth = metadata.normalWidth;
        } else if (aspectRatio > recommendedAspectRatio) {
            newWidth = metadata.normalHeight * recommendedAspectRatio;
            newHeight = metadata.normalHeight;
        }
        const shouldScale = newWidth > recommendedSize.maxWidth || newHeight > recommendedSize.maxHeight;

        const { top, left } = {
            top: Math.floor(Math.abs(metadata.normalHeight - newHeight) / 2),
            left: Math.floor(Math.abs(metadata.normalWidth - newWidth) / 2),
        };

        const extractArea = {
            top,
            left,
            width: Math.floor(newWidth - 1),
            height: Math.floor(newHeight - 1),
        } satisfies CropOptions;
        let result = image.rotate().extract(extractArea);

        if (shouldScale) {
            result = result.resize({
                width: recommendedSize.maxWidth,
                height: recommendedSize.maxHeight,
                fit: 'cover',
            });
        }

        await result.toFile(outputPath);
        fs.unlinkSync(inputPath);
        result = this._imageResizer.initialize(outputPath, outputPath);
        return result;
    }
}
