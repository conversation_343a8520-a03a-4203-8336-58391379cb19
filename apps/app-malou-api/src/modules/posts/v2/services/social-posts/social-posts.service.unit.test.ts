import { container } from 'tsyringe';

import { DbId, newDbId, toDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MediasRepository } from ':modules/media/medias.repository';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { SocialPostsService } from ':modules/posts/v2/services/social-posts/social-posts.service';

describe('SocialPostsService', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['PostsRepository', 'MediasRepository']);
    });

    describe('deleteById', () => {
        it('should delete a social post', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [getDefaultPost().source(PostSource.SOCIAL).build()];
                        },
                    },
                },
                expectedResult(): { success: boolean } {
                    return { success: true };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();

            const socialPostsService = container.resolve(SocialPostsService);
            const result = await socialPostsService.deleteById(postId);
            const expectedResult = testCase.getExpectedResult();

            expect(result).toEqual(expectedResult);

            const postsRepository = container.resolve(PostsRepository);
            const post = await postsRepository.findOne({ filter: { _id: toDbId(postId) } });

            expect(post).toBeNull();
        });

        it('should not delete a SEO post', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [getDefaultPost().source(PostSource.SEO).build()];
                        },
                    },
                },
                expectedResult(): { success: boolean } {
                    return { success: false };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();

            const socialPostsService = container.resolve(SocialPostsService);
            const result = await socialPostsService.deleteById(postId);
            const expectedResult = testCase.getExpectedResult();

            expect(result).toEqual(expectedResult);

            const postsRepository = container.resolve(PostsRepository);
            const post = await postsRepository.findOne({ filter: { _id: toDbId(postId) } });

            expect(post).not.toBeNull();
        });

        it('should be successful if the post does not exist', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult(): { success: boolean } {
                    return { success: true };
                },
            });

            await testCase.build();

            const postId = newDbId().toString();

            const socialPostsService = container.resolve(SocialPostsService);
            const result = await socialPostsService.deleteById(postId);
            const expectedResult = testCase.getExpectedResult();

            expect(result).toEqual(expectedResult);

            const postsRepository = container.resolve(PostsRepository);
            const post = await postsRepository.findOne({ filter: { _id: toDbId(postId) } });

            expect(post).toBeNull();
        });

        it('should call agenda to cancel jobs', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [getDefaultPost().source(PostSource.SOCIAL).published(PostPublicationStatus.DRAFT).build()];
                        },
                    },
                },
            });

            await testCase.build();
            const postId = testCase.getSeededObjects().posts[0]._id.toString();

            const cancelMock = jest.fn();
            const agendaSingletonMock = {
                getInstance: jest.fn().mockResolvedValue({ cancel: cancelMock }),
            } as unknown as AgendaSingleton;
            container.registerInstance(AgendaSingleton, agendaSingletonMock);

            const socialPostsService = container.resolve(SocialPostsService);
            await socialPostsService.deleteById(postId);

            expect(cancelMock).toHaveBeenCalledTimes(2);
            expect(cancelMock).toHaveBeenCalledWith({ name: AgendaJobName.PREPARE_POST, 'data.postId': postId });
            expect(cancelMock).toHaveBeenCalledWith({ name: AgendaJobName.PUBLISH_POST, 'data.postId': postId });
        });

        it('should remove the postId from medias', async () => {
            const mediaId = newDbId();

            const testCase = new TestCaseBuilderV2<'posts' | 'medias'>({
                seeds: {
                    posts: {
                        data() {
                            return [getDefaultPost().source(PostSource.SOCIAL).attachments([mediaId]).build()];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [getDefaultMedia().postIds([dependencies.posts()[0]._id])._id(mediaId).build()];
                        },
                    },
                },
                expectedResult(): DbId[] {
                    return [];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();

            const socialPostsService = container.resolve(SocialPostsService);
            await socialPostsService.deleteById(postId);

            const mediasRepository = container.resolve(MediasRepository);
            const media = await mediasRepository.findOne({ filter: { _id: mediaId } });
            const expectedResult = testCase.getExpectedResult();
            expect(media?.postIds).toEqual(expectedResult);
        });

        it('should delete the post on the platform if the method exists', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .key(PlatformKey.FACEBOOK)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(): DbId[] {
                    return [];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();

            const deletePostMock = jest.fn();
            const facebookPostsUseCasesMock = {
                deletePost: deletePostMock,
            } as unknown as FacebookPostsUseCases;
            container.registerInstance(FacebookPostsUseCases, facebookPostsUseCasesMock);

            const socialPostsService = container.resolve(SocialPostsService);
            await socialPostsService.deleteById(postId);

            expect(deletePostMock).toHaveBeenCalledExactlyOnceWith({ post: expect.objectContaining({ _id: toDbId(postId) }) });
        });
    });
});
