import { singleton } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';

import { GenerateReviewRelevantBricksService } from ':modules/ai/services/generate-review-relevant-bricks.service';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class UpdateReviewRelevantBricksService {
    constructor(
        private readonly _generateReviewRelevantBricksService: GenerateReviewRelevantBricksService,
        private readonly _reviewsRepository: ReviewsRepository
    ) {}

    async execute({ reviewId, userId }: { reviewId: string; userId?: string }): Promise<IReview> {
        const reviewRestaurant = await this._reviewsRepository.findOneOrFail({
            filter: { _id: toDbId(reviewId) },
            projection: { restaurantId: 1 },
            options: { lean: true },
        });
        const { relevantBricks, reviewRelatedBricksCount } = await this._generateReviewRelevantBricksService.execute({
            relatedEntityId: reviewId,
            restaurantId: reviewRestaurant.restaurantId.toString(),
            userId,
        });

        return this._reviewsRepository.findOneAndUpdateOrFail({
            filter: { _id: toDbId(reviewId) },
            update: { aiRelevantBricks: relevantBricks, aiRelatedBricksCount: reviewRelatedBricksCount },
            options: { lean: true },
        });
    }
}
