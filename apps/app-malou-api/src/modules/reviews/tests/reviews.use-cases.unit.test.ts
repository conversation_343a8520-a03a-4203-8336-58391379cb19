import { omit } from 'lodash';
import { container } from 'tsyringe';

import { DbId, IPlatform, IRestaurant, IUserWithProfilePicture, newDbId, OverwriteOrAssign } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostedStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { registerRepositories, TestCaseBuilder, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurantAiSettings } from ':modules/restaurant-ai-settings/tests/restaurant-ai-settings.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import GmbReviewsUseCases from ':modules/reviews/platforms/gmb/use-cases';
import UbereatsReviewsUseCases from ':modules/reviews/platforms/ubereats/use-cases';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';
import { SlackService } from ':services/slack.service';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

class BuildKeywordAnalysisForCommentServiceMock {
    execute() {
        return {
            keywords: [],
            score: 0,
            count: 0,
        };
    }
}

describe('reviewsUseCases', () => {
    beforeAll(() => {
        registerRepositories([
            'ReviewsRepository',
            'PlatformsRepository',
            'GmbCredentialsRepository',
            'RestaurantsRepository',
            'CredentialsRepository',
            'RestaurantAiSettingsRepository',
        ]);
        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });

    describe('getSortingFieldByPlatformKey', () => {
        it('should return a socialCreatedAt for random string', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            expect(reviewsUseCases.getSortingFieldByPlatformKey('randomword')).toEqual('socialCreatedAt');
        });

        it('should return a socialCreatedAt for empty string', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            expect(reviewsUseCases.getSortingFieldByPlatformKey('')).toEqual('socialCreatedAt');
        });

        it('should return a socialUpdatedAt for platform gmb', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            expect(reviewsUseCases.getSortingFieldByPlatformKey(PlatformKey.GMB)).toEqual('socialUpdatedAt');
        });
    });

    describe('getEstimatedReviewsCount', () => {
        it('works as expected', async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            expect(await reviewsUseCases.getEstimatedReviewsCount({})).toEqual(0);

            const testcase = new TestCaseBuilder<'reviews' | 'platforms' | 'restaurants' | 'credentials'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    credentials: {
                        data() {
                            return [{ _id: newDbId(), key: PlatformKey.GMB, authId: '<EMAIL>' }];
                        },
                    },

                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .credentials([dependencies.credentials![0]._id])
                                    .build(),
                            ];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .platformId(dependencies.platforms![0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies) {
                    return undefined;
                },
            });

            await testcase.build();
            const restaurant = testcase.getSeededObjects().restaurants![0] as any;

            const filters: ReviewFiltersInput = {
                restaurantIds: [restaurant._id.toString()],
                answered: true,
                ratings: [0, 1, 2, 3, 4, 5],
            };
            expect(await reviewsUseCases.getEstimatedReviewsCount(filters)).toEqual(1);
        });
    });

    describe('handleAutoReply', () => {
        it(`should return undefined when no review found`, async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = await reviewsUseCases.handleAutoReply({
                reviewId: newDbId().toString(),
                replyText: 'Reply text',
                interactionId: undefined,
                templateId: undefined,
            });

            expect(result).toEqual(undefined);
        });

        it('should return undefined when review is already answered', async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const testcase = new TestCaseBuilder<'reviews' | 'platforms' | 'restaurants' | 'credentials'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    credentials: {
                        data() {
                            return [{ key: PlatformKey.GMB, authId: '<EMAIL>', _id: newDbId() }];
                        },
                    },

                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .credentials([dependencies.credentials![0]._id])
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .platformId(dependencies.platforms![0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies) {
                    return undefined;
                },
            });

            await testcase.build();
            const review = testcase.getSeededObjects().reviews![0];
            const expectedResult = testcase.getExpectedResult();

            const result = await reviewsUseCases.handleAutoReply({
                reviewId: review._id.toString(),
                replyText: 'Reply text',
                interactionId: undefined,
                templateId: undefined,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return review mapped when answered correctly', async () => {
            const templateId = newDbId();
            const aiInteractionId = newDbId();
            class ExperimentationServiceMock {
                isFeatureAvailable = jest.fn().mockResolvedValue(false);
            }
            container.register(ExperimentationService, { useValue: new ExperimentationServiceMock() as unknown as ExperimentationService });
            container.register(GmbApiProviderUseCases, {
                useValue: {
                    updateReviewReply: async (_credentialId, _socialId, _comment) => ({
                        comment: 'Reply text',
                        updateTime: '2023-12-08T14:01:21.077871Z',
                    }),
                } as unknown as GmbApiProviderUseCases,
            });
            container.register(GenerateKeywordAnalysisForCommentService, {
                useValue: new BuildKeywordAnalysisForCommentServiceMock() as unknown as GenerateKeywordAnalysisForCommentService,
            });

            container.register(GenerateLanguageDetectionService, {
                useValue: {
                    execute: async () => ({
                        shortLang: 'fr',
                    }),
                } as unknown as GenerateLanguageDetectionService,
            });
            // initialize usecase
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            // build testcase
            const testcase = new TestCaseBuilderV2({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    credentials: {
                        data() {
                            return [{ key: PlatformKey.GMB, authId: '<EMAIL>', _id: newDbId() }];
                        },
                    },

                    platforms: {
                        data(deps) {
                            return [
                                {
                                    _id: newDbId(),
                                    key: PlatformKey.GMB,
                                    restaurantId: deps.restaurants()[0]._id,
                                    socialId: '123',
                                    apiEndpoint: 'apiEndpoint',
                                    credentials: [deps.credentials()[0]._id],
                                },
                            ];
                        },
                    },
                    reviews: {
                        data: (deps) => [
                            {
                                _id: newDbId(),
                                platformId: deps.platforms()[0]._id,
                                restaurantId: deps.platforms()[0].restaurantId,
                                key: PlatformKey.GMB,
                                socialCreatedAt: new Date(),
                                socialSortDate: new Date(),
                                socialId: 'socialId',
                                comments: [],
                            },
                        ],
                    },
                    restaurantAiSettings: {
                        data: (deps) => [getDefaultRestaurantAiSettings().restaurantId(deps.restaurants()[0]._id).build()],
                    },
                },
                expectedResult(dependencies) {
                    return {
                        _id: dependencies.reviews[0]._id,
                        platformId: dependencies.platforms[0]._id,
                        restaurantId: dependencies.platforms[0].restaurantId,
                        archived: false,
                        comments: [
                            {
                                aiInteractionIdUsed: aiInteractionId,
                                author: null,
                                isMalou: true,
                                keywordAnalysis: { count: 0, keywords: [], score: 0 },
                                posted: PostedStatus.POSTED,
                                retries: 0,
                                socialTranslatedText: null,
                                socialUpdatedAt: new Date('2023-12-08T14:01:21.077871Z'),
                                templateIdUsed: templateId,
                                text: 'Reply text',
                            },
                        ],
                        key: PlatformKey.GMB,
                        rating: null,
                        socialAttachments: [],
                        socialRating: null,
                        wasAnsweredAutomatically: true,
                    };
                },
            });

            // build testcase and await for seeds
            await testcase.build();

            // get review from testcase (our input)
            const review = testcase.getSeededObjects().reviews[0];
            // get expected result from testcase (our output)
            const expectedResult = testcase.getExpectedResult();

            // act phase
            const result = await reviewsUseCases.handleAutoReply({
                reviewId: review._id.toString(),
                replyText: 'Reply text',
                interactionId: aiInteractionId,
                templateId: templateId,
            });

            // assert phase
            expect(result).toMatchObject(expectedResult);
        });

        it('should not save templateIdUsed if passed as undefined', async () => {
            const aiInteractionId = newDbId();
            class ExperimentationServiceMock {
                isFeatureAvailable = jest.fn().mockResolvedValue(false);
            }
            container.register(ExperimentationService, { useValue: new ExperimentationServiceMock() as unknown as ExperimentationService });
            container.register(GmbApiProviderUseCases, {
                useValue: {
                    updateReviewReply: async (_credentialId, _socialId, _comment) => ({
                        comment: 'Reply text',
                        updateTime: '2023-12-08T14:01:21.077871Z',
                    }),
                } as unknown as GmbApiProviderUseCases,
            });
            container.register(GenerateKeywordAnalysisForCommentService, {
                useValue: new BuildKeywordAnalysisForCommentServiceMock() as unknown as GenerateKeywordAnalysisForCommentService,
            });

            container.register(GenerateLanguageDetectionService, {
                useValue: {
                    execute: async () => ({
                        shortLang: 'fr',
                    }),
                } as unknown as GenerateLanguageDetectionService,
            });
            // initialize usecase
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            // build testcase
            const testcase = new TestCaseBuilderV2({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    credentials: {
                        data() {
                            return [{ key: PlatformKey.GMB, authId: '<EMAIL>', _id: newDbId() }];
                        },
                    },

                    platforms: {
                        data(deps) {
                            return [
                                {
                                    _id: newDbId(),
                                    key: PlatformKey.GMB,
                                    restaurantId: deps.restaurants()[0]._id,
                                    socialId: '123',
                                    apiEndpoint: 'apiEndpoint',
                                    credentials: [deps.credentials()[0]._id],
                                },
                            ];
                        },
                    },
                    reviews: {
                        data: (deps) => [
                            {
                                _id: newDbId(),
                                platformId: deps.platforms()[0]._id,
                                restaurantId: deps.platforms()[0].restaurantId,
                                key: PlatformKey.GMB,
                                socialCreatedAt: new Date(),
                                socialSortDate: new Date(),
                                socialId: 'socialId',
                                comments: [],
                            },
                        ],
                    },
                    restaurantAiSettings: {
                        data: (deps) => [getDefaultRestaurantAiSettings().restaurantId(deps.restaurants()[0]._id).build()],
                    },
                },
                expectedResult(dependencies) {
                    return {
                        _id: dependencies.reviews[0]._id,
                        platformId: dependencies.platforms[0]._id,
                        restaurantId: dependencies.platforms[0].restaurantId,
                        archived: false,
                        comments: [
                            {
                                aiInteractionIdUsed: aiInteractionId,
                                author: null,
                                isMalou: true,
                                keywordAnalysis: { count: 0, keywords: [], score: 0 },
                                posted: PostedStatus.POSTED,
                                retries: 0,
                                socialTranslatedText: null,
                                socialUpdatedAt: new Date('2023-12-08T14:01:21.077871Z'),
                                templateIdUsed: null,
                                text: 'Reply text',
                            },
                        ],
                        key: PlatformKey.GMB,
                        rating: null,
                        socialAttachments: [],
                        socialRating: null,
                        wasAnsweredAutomatically: true,
                    };
                },
            });

            // build testcase and await for seeds
            await testcase.build();

            // get review from testcase (our input)
            const review = testcase.getSeededObjects().reviews[0];
            // get expected result from testcase (our output)
            const expectedResult = testcase.getExpectedResult();

            // act phase
            const result = await reviewsUseCases.handleAutoReply({
                reviewId: review._id.toString(),
                replyText: 'Reply text',
                interactionId: aiInteractionId,
                templateId: undefined,
            });

            // assert phase
            expect(result).toMatchObject(expectedResult);
        });
    });

    describe('reply', () => {
        it(`should throw if review not found`, async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);
            const call = async () =>
                await reviewsUseCases.reply({
                    user: {} as IUserWithProfilePicture,
                    reviewId: newDbId().toString(),
                    comment: { text: 'Reply text' },
                    restaurantId: newDbId().toString(),
                    headerConfig: {},
                    isFromJob: true,
                });

            await expect(call).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.REVIEW_NOT_FOUND,
                })
            );
        });

        it(`should throw if reply() throw and isFromJob is true`, async () => {
            const testcase = new TestCaseBuilderV2<'reviews' | 'platforms'>({
                seeds: {
                    reviews: {
                        data(deps) {
                            return [
                                {
                                    _id: newDbId(),
                                    platformId: deps.platforms()[0]._id,
                                    restaurantId: newDbId(),
                                    key: PlatformKey.GMB,
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'socialId',
                                },
                            ];
                        },
                    },
                    platforms: {
                        data: () => [
                            {
                                _id: newDbId(),
                                key: PlatformKey.GMB,
                                restaurantId: newDbId(),
                                credentials: [newDbId()],
                                socialId: '1',
                            },
                        ],
                    },
                },
                expectedResult() {
                    return undefined;
                },
            });
            await testcase.build();

            const reviewId = testcase.getSeededObjects().reviews[0]._id as DbId;

            const errorMessage = 'gmb reviews use cases error';
            container.register(GmbReviewsUseCases, {
                useValue: {
                    reply() {
                        throw new Error(errorMessage);
                    },
                } as unknown as GmbReviewsUseCases,
            });
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const call = async () =>
                await reviewsUseCases.reply({
                    user: {} as IUserWithProfilePicture,
                    reviewId: reviewId.toString(),
                    comment: { text: 'Reply text' },
                    restaurantId: newDbId().toString(),
                    headerConfig: {},
                    isFromJob: true,
                });

            await expect(call).rejects.toThrowError(errorMessage);
        });

        it(`should throw if reply() throw and isFromJob is false and platform is not Ubereats`, async () => {
            const testcase = new TestCaseBuilderV2<'reviews' | 'platforms'>({
                seeds: {
                    reviews: {
                        data(deps) {
                            return [
                                {
                                    _id: newDbId(),
                                    platformId: deps.platforms()[0]._id,
                                    restaurantId: newDbId(),
                                    key: PlatformKey.GMB,
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'socialId',
                                },
                            ];
                        },
                    },
                    platforms: {
                        data: () => [
                            {
                                _id: newDbId(),
                                key: PlatformKey.GMB,
                                restaurantId: newDbId(),
                                credentials: [newDbId()],
                                socialId: '1',
                            },
                        ],
                    },
                },
                expectedResult(_dependencies) {
                    return undefined;
                },
            });
            await testcase.build();

            const reviewId = testcase.getSeededObjects().reviews[0]._id as DbId;

            const errorMessage = 'gmb reviews use cases error';
            container.register(GmbReviewsUseCases, {
                useValue: {
                    reply() {
                        throw new Error(errorMessage);
                    },
                } as unknown as GmbReviewsUseCases,
            });
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const call = async () =>
                await reviewsUseCases.reply({
                    user: {} as IUserWithProfilePicture,
                    reviewId: reviewId.toString(),
                    comment: { text: 'Reply text' },
                    restaurantId: newDbId().toString(),
                    headerConfig: {},
                    isFromJob: false,
                });

            await expect(call).rejects.toThrowError(errorMessage);
        });

        it(`should add a comment with Retry status if reply() throw and isFromJob is false and platform is Ubereats`, async () => {
            const testcase = new TestCaseBuilderV2<'reviews' | 'platforms'>({
                seeds: {
                    reviews: {
                        data(deps) {
                            return [
                                {
                                    _id: newDbId(),
                                    platformId: deps.platforms()[0]._id,
                                    restaurantId: newDbId(),
                                    key: PlatformKey.UBEREATS,
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                },
                            ];
                        },
                    },
                    platforms: {
                        data: () => [
                            {
                                _id: newDbId(),
                                key: PlatformKey.UBEREATS,
                                restaurantId: newDbId(),
                                credentials: [newDbId()],
                                socialId: '1',
                            },
                        ],
                    },
                },
                expectedResult(_dependencies) {
                    return undefined;
                },
            });
            await testcase.build();

            const reviewId = testcase.getSeededObjects().reviews[0]._id as DbId;

            const errorMessage = 'ubereats reviews use cases error';
            container.register(UbereatsReviewsUseCases, {
                useValue: {
                    reply() {
                        throw new Error(errorMessage);
                    },
                } as unknown as UbereatsReviewsUseCases,
            });
            const agendaScheduleMock = jest.fn();
            container.register(AgendaSingleton, {
                useValue: {
                    schedule: agendaScheduleMock,
                } as unknown as AgendaSingleton,
            });
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = await reviewsUseCases.reply({
                user: {} as IUserWithProfilePicture,
                reviewId: reviewId.toString(),
                comment: { text: 'Reply text' },
                restaurantId: newDbId().toString(),
                headerConfig: {},
                isFromJob: false,
            });

            expect(result!.comments[0].posted).toEqual(PostedStatus.RETRY);
            expect(agendaScheduleMock).toHaveBeenCalledTimes(1);
        });
    });

    describe('getAnsweredAndNotAnsweredReviews', () => {
        it('should return empty arrays when no reviews', async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = await reviewsUseCases.getAnsweredAndNotAnsweredReviews({});

            expect(result).toEqual({ answered: [], notAnswered: [] });
        });

        it('should return answered and notAnswered reviews', async () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);
            const restaurantId = newDbId();
            const testCaseBuilder = new TestCaseBuilder<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.GMB,
                                    text: 'Review text',
                                    comments: [
                                        {
                                            socialId: '1',
                                            text: 'Review text',
                                            posted: PostedStatus.POSTED,
                                        },
                                    ],
                                },
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.GMB,
                                    text: 'Review text',
                                },
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.LAFOURCHETTE,
                                    text: 'Review text',
                                },
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.LAFOURCHETTE,
                                    text: 'Review text',
                                    comments: [
                                        {
                                            socialId: '1',
                                            text: 'Review text',
                                            posted: PostedStatus.POSTED,
                                        },
                                    ],
                                },
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.LAFOURCHETTE,
                                },
                                {
                                    _id: newDbId(),
                                    restaurantId,
                                    platformId: newDbId(),
                                    socialCreatedAt: new Date(),
                                    socialSortDate: new Date(),
                                    socialId: 'randomid',
                                    key: PlatformKey.FOURSQUARE,
                                },
                            ];
                        },
                    },
                },
                expectedResult(_dependencies) {
                    return {
                        answered: [_dependencies.reviews[0], _dependencies.reviews[3]].map((r) => ({
                            _id: r._id,
                            text: r.text,
                            key: r.key,
                            comments: r.comments.map((comment) => omit(comment, 'id')),
                        })),
                        notAnswered: [_dependencies.reviews[1], _dependencies.reviews[2]].map((r) => ({
                            _id: r._id,
                            text: r.text,
                            key: r.key,
                            comments: r.comments.map((comment) => omit(comment, 'id')),
                        })),
                    };
                },
            });

            await testCaseBuilder.build();

            const expectedResult = testCaseBuilder.getExpectedResult();

            const result = await reviewsUseCases.getAnsweredAndNotAnsweredReviews({});

            expect(result.answered).toIncludeSameMembers(
                expectedResult.answered.map((r) => ({
                    _id: r._id,
                    text: r.text,
                    key: r.key,
                    comments: r.comments,
                }))
            );
            expect(result.notAnswered).toIncludeSameMembers(
                expectedResult.notAnswered.map((r) => ({
                    _id: r._id,
                    text: r.text,
                    key: r.key,
                    comments: r.comments,
                }))
            );
        });
    });

    describe('getPlatformsToFetch', () => {
        const platforms: OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[] = [
            { key: 'platform1', restaurant: { active: true } },
            { key: 'platform2', restaurant: { active: false } },
            { key: 'platform3', restaurant: null },
        ] as unknown as OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[];
        const reviewsPlatformsKeys = ['platform1', 'platform3'];

        it('returns platforms that have keys in reviewsPlatformsKeys and are active', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch(platforms, reviewsPlatformsKeys, true);

            expect(result).toHaveLength(1);
            expect(result[0].key).toEqual('platform1');
        });

        it('returns platforms that have keys in reviewsPlatformsKeys and are active because active is set to true by default', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch(platforms, reviewsPlatformsKeys);

            expect(result).toHaveLength(1);
            expect(result[0].key).toEqual('platform1');
        });

        it('returns all platforms that have keys in reviewsPlatformsKeys', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch(platforms, reviewsPlatformsKeys, false);

            expect(result).toHaveLength(2);
            expect(result[0].key).toEqual('platform1');
        });

        it('returns an empty array if platforms is empty', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch([], reviewsPlatformsKeys);

            expect(result).toEqual([]);
        });

        it('returns an empty array if reviewsPlatformsKeys is empty', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch(platforms, []);

            expect(result).toEqual([]);
        });

        it('returns an empty array if platforms and reviewsPlatformsKeys are empty', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch([], []);

            expect(result).toEqual([]);
        });

        it('returns an empty array if no platform has a key in reviewsPlatformsKeys', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getPlatformsToFetch(platforms, ['platform4']);

            expect(result).toEqual([]);
        });
    });

    describe('getRestaurantPlatforms', () => {
        const platforms: OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[] = [
            { restaurantId: 1, key: 'platform1', restaurant: { id: 1, name: 'Restaurant 1' } },
            { restaurantId: 1, key: 'platform2', restaurant: { id: 1, name: 'Restaurant 1' } },
            { restaurantId: 2, key: 'platform3', restaurant: { id: 2, name: 'Restaurant 2' } },
        ] as unknown as OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[];

        it('returns an object with restaurant IDs as keys', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getRestaurantPlatforms(platforms);

            expect(Object.keys(result)).toEqual(['1', '2']);
        });

        it('returns an object with the correct structure', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getRestaurantPlatforms(platforms);

            expect(result).toEqual({
                '1': {
                    restaurant: { id: 1, name: 'Restaurant 1' },
                    platformKeys: ['platform1', 'platform2'],
                },
                '2': {
                    restaurant: { id: 2, name: 'Restaurant 2' },
                    platformKeys: ['platform3'],
                },
            });
        });

        it('handles platforms with null restaurant IDs', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);
            const nullRestaurantPlatforms = [
                { restaurantId: null, key: 'platform1', restaurant: { id: 1, name: 'Restaurant 1' } },
                { restaurantId: null, key: 'platform2', restaurant: { id: 1, name: 'Restaurant 1' } },
                { restaurantId: 2, key: 'platform3', restaurant: { id: 2, name: 'Restaurant 2' } },
            ] as unknown as OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[];

            const result = reviewsUseCases.getRestaurantPlatforms(nullRestaurantPlatforms);

            expect(Object.keys(result)).toEqual(['2']);
            expect(result).toEqual({
                '2': {
                    restaurant: { id: 2, name: 'Restaurant 2' },
                    platformKeys: ['platform3'],
                },
            });
        });

        it('handles empty platforms array', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);

            const result = reviewsUseCases.getRestaurantPlatforms([]);

            expect(result).toEqual({});
        });

        it('handles platforms with null restaurant', () => {
            const reviewsUseCases = container.resolve(ReviewsUseCases);
            const nullRestaurantPlatforms = [
                { restaurantId: 1, key: 'platform1', restaurant: null },
                { restaurantId: 2, key: 'platform2', restaurant: null },
            ] as unknown as OverwriteOrAssign<IPlatform, { restaurant: IRestaurant }>[];

            const result = reviewsUseCases.getRestaurantPlatforms(nullRestaurantPlatforms);

            expect(Object.keys(result)).toEqual(['1', '2']);
            expect(result).toEqual({
                '1': {
                    restaurant: null,
                    platformKeys: ['platform1'],
                },
                '2': {
                    restaurant: null,
                    platformKeys: ['platform2'],
                },
            });
        });
    });
});
