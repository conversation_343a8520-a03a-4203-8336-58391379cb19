import { container } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';
import { DbId } from '@malou-io/package-models';
import { ApplicationLanguage, PlatformKey, ReviewType, TranslationSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AddTranslationToReviewUseCase } from ':modules/reviews/use-cases/add-translation-to-review/add-translation-to-review.use-case';

import { getDefaultTranslations } from '../../keywords/tests/translations.builder';
import { getDefaultPrivateReview } from './private-reviews.builder';
import { getDefaultReview } from './reviews.builder';

describe('AddTranslationToReviewUseCase', () => {
    beforeAll(() => {
        registerRepositories(['TranslationsRepository', 'ReviewsRepository', 'PrivateReviewsRepository']);
    });

    describe('execute', () => {
        it('should create new translations and link it to the review', async () => {
            const addTranslationToReviewUseCase = container.resolve(AddTranslationToReviewUseCase);

            const text = 'Ceci est un test de traduction';
            const language = ApplicationLanguage.FR;
            const source = TranslationSource.SERVERLESS_AI_TEXT_GENERATOR;

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview().lang(ApplicationLanguage.EN).text('Random text').build()];
                        },
                    },
                },
                expectedResult(dependencies): ReviewWithTranslationsResponseDto {
                    return {
                        id: dependencies.reviews[0]._id.toString(),
                        _id: dependencies.reviews[0]._id.toString(),
                        key: dependencies.reviews[0].key,
                        keywordsLang: undefined,
                        socialId: dependencies.reviews[0].socialId,
                        archived: dependencies.reviews[0].archived,
                        platformId: dependencies.reviews[0].platformId.toString(),
                        restaurantId: dependencies.reviews[0].restaurantId.toString(),
                        socialLink: dependencies.reviews[0].socialLink ?? undefined,
                        businessSocialLink: dependencies.reviews[0].businessSocialLink,
                        type: dependencies.reviews[0].type as ReviewType,
                        text: dependencies.reviews[0].text ?? undefined,
                        socialTranslatedText: dependencies.reviews[0].socialTranslatedText ?? undefined,
                        title: dependencies.reviews[0].title,
                        rating: dependencies.reviews[0].rating ?? undefined,
                        socialRating: dependencies.reviews[0].socialRating ?? undefined,
                        lang: dependencies.reviews[0].lang ?? undefined,
                        aiRelatedBricksCount: dependencies.reviews[0].aiRelatedBricksCount,
                        aiRelevantBricks: dependencies.reviews[0].aiRelevantBricks!.map((brick) => ({
                            ...brick,
                            translationsId: brick.translationsId?.toString(),
                        })),
                        comments: dependencies.reviews[0].comments.map((comment) => ({
                            ...comment,
                            id: comment._id.toString(),
                            _id: comment._id.toString(),
                            socialId: comment.socialId,
                            content: undefined,
                            socialTranslatedText: comment.socialTranslatedText ?? undefined,
                            keywordAnalysis: comment.keywordAnalysis
                                ? {
                                      ...comment.keywordAnalysis,
                                      score: comment.keywordAnalysis.score ?? 0,
                                  }
                                : undefined,
                            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
                            user: comment.user
                                ? {
                                      socialId: comment.user.socialId ?? '',
                                      displayName: comment.user.displayName ?? undefined,
                                  }
                                : undefined,
                            isMalou: comment.isMalou ?? false,
                            author: comment.author
                                ? {
                                      _id: comment.author._id?.toString(),
                                      name: comment.author.name ?? undefined,
                                      picture: comment.author.picture ?? undefined,
                                  }
                                : undefined,
                            templateIdUsed: comment.templateIdUsed?.toString(),
                            retries: comment.retries,
                            aiInteractionIdUsed: comment.aiInteractionIdUsed?.toString(),
                        })),
                        ratingTags: [],
                        socialCreatedAt: dependencies.reviews[0].socialCreatedAt?.toISOString(),
                        socialUpdatedAt: dependencies.reviews[0].socialUpdatedAt?.toISOString(),
                        socialAttachments:
                            dependencies.reviews[0].socialAttachments?.map((attachment) => ({
                                urls: attachment.urls,
                                type: attachment.type,
                            })) ?? [],
                        wasAnsweredAutomatically: dependencies.reviews[0].wasAnsweredAutomatically ?? false,
                        platformPresenceStatus: dependencies.reviews[0].platformPresenceStatus,
                        translations: {
                            id: expect.any(String),
                            [language]: text,
                            en: dependencies.reviews[0].text ?? undefined,
                            es: undefined,
                            it: undefined,
                            language: dependencies.reviews[0].lang as ApplicationLanguage,
                            source,
                        },
                        reviewer: dependencies.reviews[0].reviewer as any,
                        semanticAnalysis: undefined,
                        semanticAnalysisSegments: undefined,
                        semanticAnalysisFetchStatus: null,
                        intelligentSubjects: [],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            const result = await addTranslationToReviewUseCase.execute(reviewId, text, language, source, false);

            expect(result).toStrictEqual(expectedResult);
        });

        it('should create new translations and link it to the private review', async () => {
            const addTranslationToReviewUseCase = container.resolve(AddTranslationToReviewUseCase);

            const text = 'Ceci est un test de traduction';
            const language = ApplicationLanguage.FR;
            const source = TranslationSource.SERVERLESS_AI_TEXT_GENERATOR;

            const testCase = new TestCaseBuilderV2<'privateReviews'>({
                seeds: {
                    privateReviews: {
                        data() {
                            return [getDefaultPrivateReview().lang(ApplicationLanguage.EN).text('Random text').build()];
                        },
                    },
                },
                expectedResult(dependencies): ReviewWithTranslationsResponseDto {
                    return {
                        id: dependencies.privateReviews[0]._id.toString(),
                        _id: dependencies.privateReviews[0]._id.toString(),
                        key: dependencies.privateReviews[0].key as PlatformKey,
                        keywordsLang: undefined,
                        archived: dependencies.privateReviews[0].archived,
                        restaurantId: dependencies.privateReviews[0].restaurantId.toString(),
                        platformId: undefined,
                        socialId: undefined,
                        socialLink: undefined,
                        businessSocialLink: undefined,
                        type: undefined,
                        title: undefined,
                        text: dependencies.privateReviews[0].text,
                        socialTranslatedText: undefined,
                        rating: dependencies.privateReviews[0].rating,
                        socialRating: undefined,
                        lang: dependencies.privateReviews[0].lang,
                        aiRelatedBricksCount: undefined,
                        aiRelevantBricks: undefined,
                        comments: dependencies.privateReviews[0].comments.map((comment) => ({
                            ...comment,
                            id: comment._id.toString(),
                            _id: comment._id.toString(),
                            socialId: undefined,
                            content: undefined,
                            socialTranslatedText: undefined,
                            keywordAnalysis: undefined,
                            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
                            user: undefined,
                            isMalou: false,
                            author: comment.author
                                ? {
                                      _id: comment.author._id.toString(),
                                      name: comment.author.name,
                                      picture: undefined,
                                  }
                                : undefined,
                            templateIdUsed: comment.templateIdUsed?.toString(),
                            retries: undefined,
                            aiInteractionIdUsed: undefined,
                            posted: undefined,
                        })),
                        wasAnsweredAutomatically: false,
                        platformPresenceStatus: undefined,
                        socialCreatedAt: dependencies.privateReviews[0].socialCreatedAt?.toISOString(),
                        socialUpdatedAt: undefined,
                        ratingTags: undefined,
                        socialAttachments: [],
                        translations: {
                            id: expect.any(String),
                            [language]: text,
                            en: dependencies.privateReviews[0].text,
                            es: undefined,
                            it: undefined,
                            language: dependencies.privateReviews[0].lang as ApplicationLanguage,
                            source,
                        },
                        reviewer: undefined,
                        semanticAnalysis: undefined,
                        semanticAnalysisSegments: undefined,
                        semanticAnalysisFetchStatus: null,
                        intelligentSubjects: [],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.privateReviews[0]._id as DbId).toString();

            const result = await addTranslationToReviewUseCase.execute(reviewId, text, language, source, true);

            expect(result).toStrictEqual(expectedResult);
        });

        it("should update the review's translations", async () => {
            const addTranslationToReviewUseCase = container.resolve(AddTranslationToReviewUseCase);

            const text = 'Ceci est un test de traduction';
            const language = ApplicationLanguage.FR;
            const source = TranslationSource.SERVERLESS_AI_TEXT_GENERATOR;

            const testCase = new TestCaseBuilderV2<'translations' | 'reviews'>({
                seeds: {
                    translations: {
                        data() {
                            return [getDefaultTranslations().en('Amazing text').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().translationsId(dependencies.translations()[0]._id).type(ReviewType.RATING).build()];
                        },
                    },
                },
                expectedResult(dependencies): ReviewWithTranslationsResponseDto {
                    return {
                        id: dependencies.reviews[0]._id.toString(),
                        _id: dependencies.reviews[0]._id.toString(),
                        key: dependencies.reviews[0].key,
                        keywordsLang: undefined,
                        socialId: dependencies.reviews[0].socialId,
                        archived: dependencies.reviews[0].archived,
                        platformId: dependencies.reviews[0].platformId.toString(),
                        restaurantId: dependencies.reviews[0].restaurantId.toString(),
                        socialLink: dependencies.reviews[0].socialLink ?? undefined,
                        businessSocialLink: dependencies.reviews[0].businessSocialLink,
                        type: dependencies.reviews[0].type as ReviewType,
                        text: dependencies.reviews[0].text ?? undefined,
                        socialTranslatedText: dependencies.reviews[0].socialTranslatedText ?? undefined,
                        title: dependencies.reviews[0].title,
                        rating: dependencies.reviews[0].rating ?? undefined,
                        socialRating: dependencies.reviews[0].socialRating ?? undefined,
                        lang: dependencies.reviews[0].lang ?? undefined,
                        aiRelatedBricksCount: dependencies.reviews[0].aiRelatedBricksCount,
                        aiRelevantBricks: dependencies.reviews[0].aiRelevantBricks!.map((brick) => ({
                            ...brick,
                            translationsId: brick.translationsId?.toString(),
                        })),
                        ratingTags: [],
                        comments: dependencies.reviews[0].comments.map((comment) => ({
                            ...comment,
                            id: comment._id.toString(),
                            _id: comment._id.toString(),
                            socialId: comment.socialId,
                            content: undefined,
                            socialTranslatedText: comment.socialTranslatedText ?? undefined,
                            keywordAnalysis: comment.keywordAnalysis
                                ? {
                                      ...comment.keywordAnalysis,
                                      score: comment.keywordAnalysis.score ?? 0,
                                  }
                                : undefined,
                            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
                            user: comment.user
                                ? {
                                      socialId: comment.user.socialId ?? '',
                                      displayName: comment.user.displayName ?? undefined,
                                  }
                                : undefined,
                            isMalou: comment.isMalou ?? false,
                            author: comment.author
                                ? {
                                      _id: comment.author._id?.toString(),
                                      name: comment.author.name ?? undefined,
                                      picture: comment.author.picture ?? undefined,
                                  }
                                : undefined,
                            templateIdUsed: comment.templateIdUsed?.toString(),
                            retries: comment.retries,
                            aiInteractionIdUsed: comment.aiInteractionIdUsed?.toString(),
                        })),
                        socialCreatedAt: dependencies.reviews[0].socialCreatedAt?.toISOString(),
                        socialUpdatedAt: dependencies.reviews[0].socialUpdatedAt?.toISOString(),
                        socialAttachments:
                            dependencies.reviews[0].socialAttachments?.map((attachment) => ({
                                urls: attachment.urls,
                                type: attachment.type,
                            })) ?? [],
                        wasAnsweredAutomatically: dependencies.reviews[0].wasAnsweredAutomatically ?? false,
                        platformPresenceStatus: dependencies.reviews[0].platformPresenceStatus,
                        translations: {
                            id: dependencies.translations[0]._id.toString(),
                            [language]: text,
                            en: dependencies.translations[0].en,
                            es: dependencies.translations[0].es,
                            it: dependencies.translations[0].it,
                            language: dependencies.translations[0].language,
                            source,
                        },
                        reviewer: dependencies.reviews[0].reviewer as any,
                        semanticAnalysis: undefined,
                        semanticAnalysisSegments: undefined,
                        semanticAnalysisFetchStatus: null,
                        intelligentSubjects: [],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            const result = await addTranslationToReviewUseCase.execute(reviewId, text, language, source, false);

            expect(result).toStrictEqual(expectedResult);
        });

        it("should update the private review's translations", async () => {
            const addTranslationToReviewUseCase = container.resolve(AddTranslationToReviewUseCase);

            const text = 'Ceci est un test de traduction';
            const language = ApplicationLanguage.FR;
            const source = TranslationSource.SERVERLESS_AI_TEXT_GENERATOR;

            const testCase = new TestCaseBuilderV2<'translations' | 'privateReviews'>({
                seeds: {
                    translations: {
                        data() {
                            return [getDefaultTranslations().en('Amazing text').build()];
                        },
                    },
                    privateReviews: {
                        data(dependencies) {
                            return [getDefaultPrivateReview().translationsId(dependencies.translations()[0]._id).build()];
                        },
                    },
                },
                expectedResult(dependencies): ReviewWithTranslationsResponseDto {
                    return {
                        id: dependencies.privateReviews[0]._id.toString(),
                        _id: dependencies.privateReviews[0]._id.toString(),
                        key: dependencies.privateReviews[0].key as PlatformKey,
                        keywordsLang: undefined,
                        archived: dependencies.privateReviews[0].archived,
                        restaurantId: dependencies.privateReviews[0].restaurantId.toString(),
                        platformId: undefined,
                        socialId: undefined,
                        socialLink: undefined,
                        businessSocialLink: undefined,
                        type: undefined,
                        title: undefined,
                        ratingTags: undefined,
                        text: dependencies.privateReviews[0].text,
                        socialTranslatedText: undefined,
                        rating: dependencies.privateReviews[0].rating,
                        socialRating: undefined,
                        lang: dependencies.privateReviews[0].lang,
                        aiRelatedBricksCount: undefined,
                        aiRelevantBricks: undefined,
                        comments: dependencies.privateReviews[0].comments.map((comment) => ({
                            ...comment,
                            id: comment._id.toString(),
                            _id: comment._id.toString(),
                            socialId: undefined,
                            content: undefined,
                            socialTranslatedText: undefined,
                            keywordAnalysis: undefined,
                            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
                            user: undefined,
                            isMalou: false,
                            author: comment.author
                                ? {
                                      _id: comment.author._id.toString(),
                                      name: comment.author.name,
                                      picture: undefined,
                                  }
                                : undefined,
                            templateIdUsed: comment.templateIdUsed?.toString(),
                            retries: undefined,
                            aiInteractionIdUsed: undefined,
                            posted: undefined,
                        })),
                        wasAnsweredAutomatically: false,
                        platformPresenceStatus: undefined,
                        socialCreatedAt: dependencies.privateReviews[0].socialCreatedAt?.toISOString(),
                        socialUpdatedAt: undefined,
                        socialAttachments: [],
                        translations: {
                            id: dependencies.translations[0]._id.toString(),
                            [language]: text,
                            en: dependencies.translations[0].en,
                            es: dependencies.translations[0].es,
                            it: dependencies.translations[0].it,
                            language: dependencies.translations[0].language,
                            source,
                        },
                        reviewer: undefined,
                        semanticAnalysis: undefined,
                        semanticAnalysisSegments: undefined,
                        semanticAnalysisFetchStatus: null,
                        intelligentSubjects: [],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.privateReviews[0]._id as DbId).toString();

            const result = await addTranslationToReviewUseCase.execute(reviewId, text, language, source, true);

            expect(result).toStrictEqual(expectedResult);
        });
    });
});
