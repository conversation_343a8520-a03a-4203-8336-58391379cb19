import { omit } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import {
    NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION,
    NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION,
    PlatformKey,
    PostedStatus,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

import { getDefaultReview } from './reviews.builder';

describe('reviewsUseCases', () => {
    beforeAll(() => {
        registerRepositories(['ReviewsRepository']);
    });

    describe('getAnsweredAndNotAnsweredReviewCountByRestaurantId', () => {
        it('should not inlcude foursquare and lafourchette without text reviews in response', async () => {
            const reviewsRepository = container.resolve(ReviewsRepository);

            const restaurantId = newDbId();

            const testCase = new TestCaseBuilder<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.GMB)
                                    .comments([
                                        {
                                            posted: PostedStatus.POSTED,
                                            text: 'replied',
                                            _id: newDbId(),
                                        },
                                    ])
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .text('hello !')
                                    .comments([
                                        {
                                            posted: PostedStatus.POSTED,
                                            text: 'replied',
                                            _id: newDbId(),
                                        },
                                    ])
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .text('hey i am answerable')
                                    .comments([])
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .comments([])
                                    .key(PlatformKey.FOURSQUARE)
                                    .text('i am not answerable')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_dependencies) {
                    return {
                        answered: 2,
                        notAnswered: 1,
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const result = await reviewsRepository.getAnsweredAndNotAnsweredReviewCountByRestaurantId({
                restaurantId: restaurantId.toString(),
                startDate: DateTime.local().minus({ days: 30 }).toJSDate(),
                endDate: DateTime.local().toJSDate(),
            });

            expect(result).toEqual(expectedResult);
        });
    });

    describe('getNegativeReviewsToBeNotified', () => {
        it('should return negative unanswered reviews to be notified', async () => {
            const reviewsRepository = container.resolve(ReviewsRepository);
            const now = DateTime.now();
            const testCase = new TestCaseBuilder<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 }).toJSDate())
                                    .text('review text 1')
                                    .comments([])
                                    .rating(3)
                                    .build(),
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 }).toJSDate())
                                    .text('review text 2')
                                    .comments([])
                                    .rating(4)
                                    .build(),
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 }).toJSDate())
                                    .text('review text 3')
                                    .comments([])
                                    .rating(5)
                                    .build(),
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 }).toJSDate())
                                    .text('review text 4')
                                    .comments([])
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION - 1 }).toJSDate())
                                    .text('review text 5')
                                    .comments([])
                                    .rating(1)
                                    .build(),
                                getDefaultReview()
                                    .socialCreatedAt(now.minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION + 1 }).toJSDate())
                                    .text('review text 6')
                                    .comments([{ posted: PostedStatus.POSTED, text: 'replied', _id: newDbId() }])
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [omit(dependencies.reviews[0], 'id'), omit(dependencies.reviews[3], 'id'), omit(dependencies.reviews[4], 'id')];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await reviewsRepository.getNegativeReviewsToBeNotified();

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});
