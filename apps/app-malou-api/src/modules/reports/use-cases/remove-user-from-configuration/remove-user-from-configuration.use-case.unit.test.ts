import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { HeapEventName, ReportType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import { encodeString } from ':helpers/utils';
import { USER_EMAILS_TO_FILTER_ON_UNSUBSCRIBE } from ':modules/reports/report.constants';
import ReportsRepository from ':modules/reports/reports.repository';
import { getDefaultReport } from ':modules/reports/tests/report.builder';
import { RemoveUserFromConfigurationUseCase } from ':modules/reports/use-cases/remove-user-from-configuration/remove-user-from-configuration.use-case';
import { HeapAnalyticsService } from ':plugins/heap-analytics';

describe('reports.remove-user-from-configuration.use-case', () => {
    const reportsRepository = container.resolve(ReportsRepository);
    const removeUserFromConfigurationUseCase = container.resolve(RemoveUserFromConfigurationUseCase);
    const heapService = container.resolve(HeapAnalyticsService);

    beforeAll(() => {
        registerRepositories(['ReportsRepository']);
    });

    it('should remove restaurantId and user emails from configs', async () => {
        const userId = newDbId();
        const userId2 = newDbId();

        const configurationId = newDbId();
        const configurationId2 = newDbId();
        const configurationId3 = newDbId();

        const trackHeapEventSpy = jest.spyOn(heapService, 'track');

        const testCase = new TestCaseBuilder<'reports'>({
            seeds: {
                reports: {
                    data() {
                        return [
                            getDefaultReport()
                                .userId(userId)
                                .configurations([
                                    {
                                        _id: configurationId,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                    },
                                    {
                                        _id: configurationId2,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.MONTHLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId)
                                .configurations([
                                    {
                                        _id: configurationId3,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.WEEKLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId2)
                                .configurations([
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.MONTHLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId2)
                                .configurations([
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.WEEKLY_REVIEWS)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return dependencies.reports.map((report) => report._id.toString());
            },
        });

        await testCase.build();
        const reportIds = testCase.getExpectedResult();

        const hash = encodeString(JSON.stringify({ configurationId: configurationId.toString(), email: '<EMAIL>' }));
        await removeUserFromConfigurationUseCase.execute({ hash });

        // Get all reports
        let reports = await reportsRepository.find({
            filter: {},
            options: {
                lean: true,
            },
        });
        const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
        expect(report1?.configurations).toHaveLength(2);
        expect(report1?.configurations[0]).toEqual(
            expect.objectContaining({
                _id: configurationId,
                recipients: ['<EMAIL>', '<EMAIL>'],
            })
        );
        expect(report1?.configurations[1]).toEqual(
            expect.objectContaining({
                _id: configurationId2,
                recipients: ['<EMAIL>'],
            })
        );
        const report2 = reports.find((r) => r._id.toString() === reportIds[2]);
        expect(report2?.configurations).toHaveLength(2);
        expect(report2?.configurations[0]).toEqual(
            expect.objectContaining({
                recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            })
        );

        expect(trackHeapEventSpy).toHaveBeenCalledWith({
            identity: '<EMAIL>',
            eventName: HeapEventName.REPORT_EMAIL_UNSUBSCRIBE,
            properties: {
                reportId: report1?._id.toString(),
                receiverEmail: '<EMAIL>',
                reportType: report1?.type,
                configurationId: configurationId.toString(),
            },
        });

        // Remove user from another configuration
        trackHeapEventSpy.mockClear();

        const hash2 = encodeString(JSON.stringify({ configurationId: configurationId3.toString(), email: '<EMAIL>' }));
        await removeUserFromConfigurationUseCase.execute({ hash: hash2 });

        // Get all reports
        reports = await reportsRepository.find({
            filter: {},
            options: {
                lean: true,
            },
        });

        // Check that email has been removed from other user's report
        const report3 = reports.find((r) => r._id.toString() === reportIds[1]);
        expect(report3?.configurations).toHaveLength(1);
        expect(report3?.configurations[0]).toEqual(
            expect.objectContaining({
                recipients: ['<EMAIL>'],
            })
        );

        expect(trackHeapEventSpy).toHaveBeenCalledWith({
            identity: '<EMAIL>',
            eventName: HeapEventName.REPORT_EMAIL_UNSUBSCRIBE,
            properties: {
                reportId: report3?._id.toString(),
                receiverEmail: '<EMAIL>',
                reportType: report3?.type,
                configurationId: configurationId3.toString(),
            },
        });

        // If I try to remove the email again, we shouldn't send another Heap event
        trackHeapEventSpy.mockClear();
        await removeUserFromConfigurationUseCase.execute({ hash: hash2 });
        expect(trackHeapEventSpy).not.toHaveBeenCalled();
    });

    it('should remove  user email from the correct config', async () => {
        const userId = newDbId();
        const userId2 = newDbId();

        const configurationId = newDbId();
        const configurationId2 = newDbId();
        const configurationId3 = newDbId();

        const trackHeapEventSpy = jest.spyOn(heapService, 'track');

        const testCase = new TestCaseBuilder<'reports'>({
            seeds: {
                reports: {
                    data() {
                        return [
                            getDefaultReport()
                                .userId(userId)
                                .configurations([
                                    {
                                        _id: configurationId,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                    },
                                    {
                                        _id: configurationId2,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.MONTHLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId)
                                .configurations([
                                    {
                                        _id: configurationId3,
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.WEEKLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId2)
                                .configurations([
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>', '<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.MONTHLY_PERFORMANCE)
                                .build(),
                            getDefaultReport()
                                .userId(userId2)
                                .configurations([
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                    {
                                        _id: newDbId(),
                                        restaurantsIds: [newDbId()],
                                        recipients: ['<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.WEEKLY_REVIEWS)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return dependencies.reports.map((report) => report._id.toString());
            },
        });

        await testCase.build();
        const reportIds = testCase.getExpectedResult();

        const hash = encodeString(JSON.stringify({ configurationId: configurationId2.toString(), email: '<EMAIL>' }));
        await removeUserFromConfigurationUseCase.execute({ hash });

        // Get all reports
        const reports = await reportsRepository.find({
            filter: {},
            options: {
                lean: true,
            },
        });
        const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
        expect(report1?.configurations).toHaveLength(2);
        expect(report1?.configurations[0]).toEqual(
            expect.objectContaining({
                _id: configurationId,
                recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            })
        );
        expect(report1?.configurations[1]).toEqual(
            expect.objectContaining({
                _id: configurationId2,
                recipients: ['<EMAIL>'],
            })
        );

        expect(trackHeapEventSpy).toHaveBeenCalledWith({
            identity: '<EMAIL>',
            eventName: HeapEventName.REPORT_EMAIL_UNSUBSCRIBE,
            properties: {
                reportId: report1?._id.toString(),
                receiverEmail: '<EMAIL>',
                reportType: report1?.type,
                configurationId: configurationId2.toString(),
            },
        });
    });

    it('should not remove user email if its filtered', async () => {
        const userId = newDbId();

        const configurationId = newDbId();

        const testCase = new TestCaseBuilder<'reports'>({
            seeds: {
                reports: {
                    data() {
                        return [
                            getDefaultReport()
                                .userId(userId)
                                .configurations([
                                    {
                                        _id: configurationId,
                                        restaurantsIds: [newDbId()],
                                        recipients: [USER_EMAILS_TO_FILTER_ON_UNSUBSCRIBE[0], '<EMAIL>'],
                                    },
                                ])
                                .type(ReportType.MONTHLY_PERFORMANCE)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return dependencies.reports.map((report) => report._id.toString());
            },
        });

        await testCase.build();

        const hash = encodeString(
            JSON.stringify({ configurationId: configurationId.toString(), email: USER_EMAILS_TO_FILTER_ON_UNSUBSCRIBE[0] })
        );
        await removeUserFromConfigurationUseCase.execute({ hash });

        const reportIds = testCase.getExpectedResult();

        // Get all reports
        const reports = await reportsRepository.find({
            filter: {},
            options: {
                lean: true,
            },
        });
        const report = reports.find((r) => r._id.toString() === reportIds[0]);
        expect(report?.configurations).toHaveLength(1);
        expect(report?.configurations[0]).toEqual(
            expect.objectContaining({
                _id: configurationId,
                recipients: [USER_EMAILS_TO_FILTER_ON_UNSUBSCRIBE[0], '<EMAIL>'],
            })
        );
    });
});
