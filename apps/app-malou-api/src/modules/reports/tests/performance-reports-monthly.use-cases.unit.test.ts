import { DateTime, Interval } from 'luxon';
import { container } from 'tsyringe';

import { IKeywordTemp, IReport, IRestaurant, IUser, newDbId } from '@malou-io/package-models';
import { BusinessCategory, MalouErrorCode, ReportType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import { digestMessage } from ':helpers/utils';
import { getDefaultGeoSample } from ':modules/keywords/modules/geolocation/tests/geosample.builder';
import { getContainingRectangle } from ':modules/keywords/modules/geolocation/utils';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { GmbPlatformInsights } from ':modules/platform-insights/platforms/gmb/use-cases';
import { Report } from ':modules/reports/report.entity';
import ReportsRepository from ':modules/reports/reports.repository';
import ReportsUseCases from ':modules/reports/reports.use-cases';
import { getDefaultReport } from ':modules/reports/tests/report.builder';
import { GetPerformanceGmbSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-gmb-section.use-case';
import { GetPerformanceKeywordsSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-keywords-section.use-case';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { User } from ':modules/users/entities/user.entity';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import { YextProvider } from ':providers/yext/yext.provider';
import { YextProviderMock } from ':providers/yext/yext.provider.mock';
import { SlackService } from ':services/slack.service';

const createReport = async (restaurants: any[], type: ReportType) => {
    const reportRepository = container.resolve(ReportsRepository);
    const restaurantRepository = container.resolve(RestaurantsRepository);
    const restaurantsInDb = await restaurantRepository.createMany({ data: restaurants });

    const userId = newDbId();
    await reportRepository.create({
        data: {
            type,
            userId,
            active: true,
            configurations: [
                {
                    _id: newDbId(),
                    restaurantsIds: restaurantsInDb.map((restaurant) => restaurant._id),
                    recipients: ['<EMAIL>'],
                },
            ],
        },
    });

    const [report] = await reportRepository.getReportsByUserId({ userId: userId.toString() });
    return report;
};

describe('GetPerformanceMonthlyReportUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'ReportsRepository',
            'UsersRepository',
            'RestaurantsRepository',
            'PlatformsRepository',
            'KeywordsTempRepository',
            'RestaurantKeywordsRepository',
            'GeoSampleRepository',
        ]);

        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });

    describe('getGmbInsightsEmailSection', () => {
        const getInsightsAggregatedMock = jest.fn();
        class GmbPlatformInsightsMock {
            async getInsightsAggregated() {
                return getInsightsAggregatedMock();
            }
        }

        let getPerformanceGmbSectionUseCase: GetPerformanceGmbSectionUseCase;

        beforeAll(() => {
            jest.clearAllMocks();
            container.register(GmbPlatformInsights, { useValue: new GmbPlatformInsightsMock() as unknown as GmbPlatformInsights });
            getPerformanceGmbSectionUseCase = container.resolve(GetPerformanceGmbSectionUseCase);
        });

        it('should be restaurant 2 worstPerformingRestaurant and should be restaurant 3 bestPerformingRestaurant', async () => {
            // current
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });

            // previous
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 110,
                        },
                        business_impressions_mobile_maps: {
                            value: 110,
                        },
                        business_impressions_desktop_maps: {
                            value: 110,
                        },
                        actions_phone: {
                            value: 110,
                        },
                        business_impressions_desktop_search: {
                            value: 110,
                        },
                        business_impressions_mobile_search: {
                            value: 110,
                        },
                        actions_website: {
                            value: 110,
                        },
                        actions_booking: {
                            value: 200,
                        },
                        actions_driving_directions: {
                            value: 200,
                        },
                    },
                };
            });

            // current
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 5,
                        },
                        business_impressions_mobile_maps: {
                            value: 5,
                        },
                        business_impressions_desktop_maps: {
                            value: 5,
                        },
                        actions_phone: {
                            value: 5,
                        },
                        business_impressions_desktop_search: {
                            value: 5,
                        },
                        business_impressions_mobile_search: {
                            value: 5,
                        },
                        actions_website: {
                            value: 5,
                        },
                        actions_booking: {
                            value: 5,
                        },
                        actions_driving_directions: {
                            value: 5,
                        },
                    },
                };
            });

            // previous
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 1000,
                        },
                        business_impressions_mobile_maps: {
                            value: 1000,
                        },
                        business_impressions_desktop_maps: {
                            value: 1000,
                        },
                        actions_phone: {
                            value: 1000,
                        },
                        business_impressions_desktop_search: {
                            value: 1000,
                        },
                        business_impressions_mobile_search: {
                            value: 1000,
                        },
                        actions_website: {
                            value: 1000,
                        },
                        actions_booking: {
                            value: 1000,
                        },
                        actions_driving_directions: {
                            value: 1000,
                        },
                    },
                };
            });

            // current
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 10000,
                        },
                        business_impressions_mobile_maps: {
                            value: 10000,
                        },
                        business_impressions_desktop_maps: {
                            value: 10000,
                        },
                        actions_phone: {
                            value: 10000,
                        },
                        business_impressions_desktop_search: {
                            value: 10000,
                        },
                        business_impressions_mobile_search: {
                            value: 10000,
                        },
                        actions_website: {
                            value: 10000,
                        },
                        actions_booking: {
                            value: 10000,
                        },
                        actions_driving_directions: {
                            value: 10000,
                        },
                    },
                };
            });

            // previous
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });

            const restaurantId1 = newDbId();
            const restaurantId2 = newDbId();
            const restaurantId3 = newDbId();
            const report = await createReport(
                [
                    {
                        _id: restaurantId1,
                        name: 'Restaurant 1',
                        type: BusinessCategory.LOCAL_BUSINESS,
                        address: {
                            administrativeArea: 'Rouen',
                            country: 'France',
                            formattedAddress: 'ROuen, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue du kiff',
                            streetNumber: '1',
                        },
                    },
                    {
                        _id: restaurantId2,
                        name: 'Restaurant 2',
                        type: BusinessCategory.LOCAL_BUSINESS,
                        address: {
                            administrativeArea: 'Paris',
                            country: 'France',
                            formattedAddress: 'Paris, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue de la Paix',
                            streetNumber: '1',
                        },
                    },
                    {
                        _id: restaurantId3,
                        name: 'Restaurant 3',
                        type: BusinessCategory.LOCAL_BUSINESS,
                        address: {
                            administrativeArea: 'Paris',
                            country: 'France',
                            formattedAddress: 'Paris, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue de la Paixeee',
                            streetNumber: '1',
                        },
                    },
                ],
                ReportType.MONTHLY_PERFORMANCE
            );

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceGmbSectionUseCase.getGmbInsightsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });
            expect(emailSectionResult.worstPerformingRestaurant?.restaurantId).toEqual(restaurantId2);
            expect(emailSectionResult.bestPerformingRestaurant?.restaurantId).toEqual(restaurantId3);
        });

        it('should be the right value for currentRatioActionsOverImpressions (32.70)', async () => {
            // current
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 200,
                        },
                        business_impressions_desktop_maps: {
                            value: 870,
                        },
                        actions_phone: {
                            value: 127,
                        },
                        business_impressions_desktop_search: {
                            value: 333,
                        },
                        business_impressions_mobile_search: {
                            value: 765,
                        },
                        actions_website: {
                            value: 373,
                        },
                        actions_booking: {
                            value: 101,
                        },
                        actions_driving_directions: {
                            value: 8,
                        },
                    },
                };
            });
            // previous
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });

            const report = await createReport(
                [
                    {
                        _id: newDbId(),
                        name: 'Restaurant 1',
                        type: BusinessCategory.LOCAL_BUSINESS,
                        address: {
                            administrativeArea: 'Rouen',
                            country: 'France',
                            formattedAddress: 'ROuen, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue du kiff',
                            streetNumber: '1',
                        },
                    },
                ],
                ReportType.MONTHLY_PERFORMANCE
            );

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceGmbSectionUseCase.getGmbInsightsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult.currentRatioActionsOverImpressions.toFixed(2)).toEqual('32.70');
        });

        it('should be the right value for previousRatioActionsOverImpressions (29.94)', async () => {
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 200,
                        },
                        business_impressions_desktop_maps: {
                            value: 870,
                        },
                        actions_phone: {
                            value: 127,
                        },
                        business_impressions_desktop_search: {
                            value: 333,
                        },
                        business_impressions_mobile_search: {
                            value: 965,
                        },
                        actions_website: {
                            value: 373,
                        },
                        actions_booking: {
                            value: 101,
                        },
                        actions_driving_directions: {
                            value: 8,
                        },
                    },
                };
            });

            const report = await createReport(
                [
                    {
                        _id: newDbId(),
                        name: 'Restaurant 1',
                        type: 'local_business',
                        address: {
                            administrativeArea: 'Rouen',
                            country: 'France',
                            formattedAddress: 'ROuen, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue du kiff',
                            streetNumber: '1',
                        },
                    },
                ],
                ReportType.MONTHLY_PERFORMANCE
            );

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceGmbSectionUseCase.getGmbInsightsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult.previousRatioActionsOverImpressions.toFixed(2)).toEqual('29.94');
        });

        it('should filter out the 3rd restaurant that threw an error', async () => {
            // current
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    error: true,
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });
            getInsightsAggregatedMock.mockImplementationOnce(() => {
                return {
                    total: {
                        actions_menu: {
                            value: 100,
                        },
                        business_impressions_mobile_maps: {
                            value: 100,
                        },
                        business_impressions_desktop_maps: {
                            value: 100,
                        },
                        actions_phone: {
                            value: 100,
                        },
                        business_impressions_desktop_search: {
                            value: 100,
                        },
                        business_impressions_mobile_search: {
                            value: 100,
                        },
                        actions_website: {
                            value: 100,
                        },
                        actions_booking: {
                            value: 100,
                        },
                        actions_driving_directions: {
                            value: 100,
                        },
                    },
                };
            });

            const report = await createReport(
                [
                    {
                        _id: newDbId(),
                        name: 'Restaurant 1',
                        type: 'local_business',
                        address: {
                            administrativeArea: 'Rouen',
                            country: 'France',
                            formattedAddress: 'ROuen, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue du kiff',
                            streetNumber: '1',
                        },
                    },
                    {
                        _id: newDbId(),
                        name: 'Restaurant 2',
                        type: 'local_business',
                        address: {
                            administrativeArea: 'Paris',
                            country: 'France',
                            formattedAddress: 'Paris, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue de la Paix',
                            streetNumber: '1',
                        },
                    },
                    {
                        _id: newDbId(),
                        name: 'Restaurant 3',
                        type: 'local_business',
                        address: {
                            administrativeArea: 'Paris',
                            country: 'France',
                            formattedAddress: 'Paris, France',
                            locality: 'Paris',
                            postalCode: '75000',
                            regionCode: '75',
                            route: 'Rue de la Paixeee',
                            streetNumber: '1',
                        },
                    },
                    {
                        _id: newDbId(),
                        name: 'Restaurant 4',
                        type: 'local_business',
                        address: {
                            administrativeArea: 'Paris',
                            country: 'France',
                            formattedAddress: 'Paris, France',
                            locality: 'Paris',
                            postalCode: '75004',
                            regionCode: '75',
                            route: 'Rue de la Paixeee grr baw',
                            streetNumber: '1',
                        },
                    },
                ],
                ReportType.MONTHLY_PERFORMANCE
            );

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceGmbSectionUseCase.getGmbInsightsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult.restaurantsInsightsDetails.length).toEqual(3);
            expect(emailSectionResult.currentTotalImpressions).toEqual(1200);
        });
    });

    describe('getKeywordsEmailSection', () => {
        let getPerformanceKeywordsSectionUseCase: GetPerformanceKeywordsSectionUseCase;

        const restaurantRepo = container.resolve(RestaurantsRepository);

        beforeAll(() => {
            jest.clearAllMocks();
            getPerformanceKeywordsSectionUseCase = container.resolve(GetPerformanceKeywordsSectionUseCase);
        });

        it('should return empty KeywordsEmailSection', async () => {
            // Arrange
            const testCaseBuilder = new TestCaseBuilder<'restaurants' | 'keywordsTemp' | 'restaurantKeywords' | 'geoSamples'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                // 48.87, 2.345 // 48.875, 2.345 // 48.875, 2.35 // 48.87, 2.35
                                getDefaultRestaurant()
                                    .name('Restau1')
                                    .placeId('placeid1')
                                    .latlng({ lat: 48.8745369, lng: 2.3452137 })
                                    .build(),
                                // 48.85, 2.375 // 48.85, 2.38 // 48.855, 2.375 // 48.855, 2.38
                                getDefaultRestaurant()
                                    .name('Restau2')
                                    .placeId('placeid2')
                                    .latlng({ lat: 48.8539839, lng: 2.3763766 })
                                    .build(),
                                // 48.875, 2.335 // 48.875, 2.34 // 48.88, 2.335 // 48.88, 2.34
                                getDefaultRestaurant()
                                    .name('Restau3')
                                    .placeId('placeid3')
                                    .latlng({ lat: 48.8780556, lng: 2.3394444 })
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('restaurant brunch').build(),
                                getDefaultKeywordTemp().text('restaurant etoile').build(),
                                getDefaultKeywordTemp().text('restaurant cool').build(),
                                getDefaultKeywordTemp().text('restaurant marrant').build(),
                                getDefaultKeywordTemp().text('restaurant grr baw').build(),
                                getDefaultKeywordTemp().text('restaurant toto').build(),
                                getDefaultKeywordTemp().text('restaurant titi').build(),
                                getDefaultKeywordTemp().text('restaurant tete').build(),
                                getDefaultKeywordTemp().text('restaurant tutu').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![1]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![2]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![3]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![4]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![5]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![6]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![7]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![8]._id)
                                    .build(),
                            ];
                        },
                    },
                    geoSamples: {
                        data(dependencies) {
                            const geoSamples = Interval.fromDateTimes(
                                DateTime.now().minus({ months: 2 }),
                                DateTime.now().plus({ weeks: 1 })
                            )
                                .splitBy({ weeks: 1 })
                                .map((interval) => {
                                    return (
                                        dependencies.restaurantKeywords?.map((restaurantKeyword) => {
                                            const keyword = dependencies.keywordsTemp?.find(
                                                (k) => k._id.toString() === restaurantKeyword.keywordId.toString()
                                            );
                                            const restaurant = dependencies.restaurants?.find(
                                                (r) => r._id.toString() === restaurantKeyword.restaurantId.toString()
                                            );
                                            const angles = getContainingRectangle(restaurant!.latlng!.lat, restaurant!.latlng!.lng);
                                            return [
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles?.tl.lat)
                                                    .lng(angles?.tl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking([
                                                        {
                                                            name: 'Restaurant a',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAA',
                                                            vicinity: 'Paris 1',
                                                        },
                                                        {
                                                            name: 'Restaurant b',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                            vicinity: 'Paris 2',
                                                        },
                                                        {
                                                            name: 'Restaurant 1',
                                                            place_id: 'placeid1',
                                                            vicinity: 'Paris 1',
                                                        },
                                                        {
                                                            name: 'Restaurant z',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAD',
                                                            vicinity: 'Paris z',
                                                        },
                                                        {
                                                            name: 'Restaurant x',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                            vicinity: 'Paris x',
                                                        },
                                                        {
                                                            name: 'Restaurant xx',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                            vicinity: 'Paris xx',
                                                        },
                                                    ])
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tr.lat)
                                                    .lng(angles.tr.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.bl.lat)
                                                    .lng(angles.bl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.br.lat)
                                                    .lng(angles.br.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .build(),
                                            ];
                                        }) ?? []
                                    );
                                });
                            return geoSamples.flat(2);
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });
            await testCaseBuilder.build();
            const seeds = testCaseBuilder.getSeededObjects();

            const restaurants = await restaurantRepo.find({
                filter: {
                    _id: { $in: seeds.restaurants?.map((r) => r._id) },
                },
            });

            const report = await createReport(restaurants, ReportType.MONTHLY_PERFORMANCE);

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceKeywordsSectionUseCase.execute({
                report,
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult).toEqual([]);
        });

        it('should return 6 keyworks evolutions', async () => {
            // Arrange
            const testCaseBuilder = new TestCaseBuilder<'restaurants' | 'keywordsTemp' | 'restaurantKeywords' | 'geoSamples'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                // 48.87, 2.345 // 48.875, 2.345 // 48.875, 2.35 // 48.87, 2.35
                                getDefaultRestaurant()
                                    .name('Restau1')
                                    .placeId('placeid1')
                                    .latlng({ lat: 48.8745369, lng: 2.3452137 })
                                    .build(),
                                // 48.85, 2.375 // 48.85, 2.38 // 48.855, 2.375 // 48.855, 2.38
                                getDefaultRestaurant()
                                    .name('Restau2')
                                    .placeId('placeid2')
                                    .latlng({ lat: 48.8539839, lng: 2.3763766 })
                                    .build(),
                                // 48.875, 2.335 // 48.875, 2.34 // 48.88, 2.335 // 48.88, 2.34
                                getDefaultRestaurant()
                                    .name('Restau3')
                                    .placeId('placeid3')
                                    .latlng({ lat: 48.8780556, lng: 2.3394444 })
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('restaurant brunch').build(),
                                getDefaultKeywordTemp().text('restaurant etoile').build(),
                                getDefaultKeywordTemp().text('restaurant cool').build(),
                                getDefaultKeywordTemp().text('restaurant marrant').build(),
                                getDefaultKeywordTemp().text('restaurant grr baw').build(),
                                getDefaultKeywordTemp().text('restaurant toto').build(),
                                getDefaultKeywordTemp().text('restaurant titi').build(),
                                getDefaultKeywordTemp().text('restaurant tete').build(),
                                getDefaultKeywordTemp().text('restaurant tutu').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![1]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![2]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![3]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![4]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![5]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![6]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![7]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![8]._id)
                                    .build(),
                            ];
                        },
                    },
                    geoSamples: {
                        data(dependencies) {
                            const geoSamples = Interval.fromDateTimes(
                                DateTime.now().minus({ months: 2 }),
                                DateTime.now().plus({ weeks: 1 })
                            )
                                .splitBy({ weeks: 1 })
                                .map((interval, idx, dates) => {
                                    return (
                                        dependencies.restaurantKeywords?.map((restaurantKeyword) => {
                                            const keyword = dependencies.keywordsTemp?.find(
                                                (k) => k._id.toString() === restaurantKeyword.keywordId.toString()
                                            );
                                            const restaurant = dependencies.restaurants?.find(
                                                (r) => r._id.toString() === restaurantKeyword.restaurantId.toString()
                                            );
                                            const angles = getContainingRectangle(restaurant!.latlng!.lat, restaurant!.latlng!.lng);
                                            return [
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tl.lat)
                                                    .lng(angles.tl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(
                                                        idx <= dates.length / 2
                                                            ? [
                                                                  {
                                                                      name: 'Restaurant A',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAA',
                                                                      vicinity: 'Paris 1',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                            : [
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                    )
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tr.lat)
                                                    .lng(angles.tr.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(
                                                        idx <= dates.length / 2
                                                            ? [
                                                                  {
                                                                      name: 'Restaurant A',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAA',
                                                                      vicinity: 'Paris 1',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                            : [
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                    )
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.bl.lat)
                                                    .lng(angles.bl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(
                                                        idx <= dates.length / 2
                                                            ? [
                                                                  {
                                                                      name: 'Restaurant A',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAA',
                                                                      vicinity: 'Paris 1',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                            : [
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                    )
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.br.lat)
                                                    .lng(angles.br.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(
                                                        idx <= dates.length / 2
                                                            ? [
                                                                  {
                                                                      name: 'Restaurant A',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAA',
                                                                      vicinity: 'Paris 1',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                            : [
                                                                  {
                                                                      name: restaurant?.name ?? '',
                                                                      place_id: restaurant?.placeId ?? '',
                                                                      vicinity: 'osef',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant B',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                                      vicinity: 'Paris 2',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant C',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                                      vicinity: 'Paris 3',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant Z',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                                      vicinity: 'Paris ZZ',
                                                                  },
                                                                  {
                                                                      name: 'Restaurant X',
                                                                      place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                                      vicinity: 'Paris XX',
                                                                  },
                                                              ]
                                                    )
                                                    .build(),
                                            ];
                                        }) ?? []
                                    );
                                });
                            return geoSamples.flat(2);
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });
            await testCaseBuilder.build();
            const seeds = testCaseBuilder.getSeededObjects();

            const restaurants = await restaurantRepo.find({
                filter: {
                    _id: { $in: seeds.restaurants?.map((r) => r._id) },
                },
            });

            const report = await createReport(restaurants, ReportType.MONTHLY_PERFORMANCE);

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceKeywordsSectionUseCase.getKeywordsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult?.length).toEqual(6);
        });

        it('should return 3 keywords sorted from best evolution to worst', async () => {
            function buildRank(
                idx: number,
                intervals: Interval[],
                restaurant: IRestaurant | undefined,
                keyword: IKeywordTemp | undefined
            ): {
                name: string;
                place_id: string;
                vicinity: string;
            }[] {
                if (restaurant?.placeId !== 'placeid1') {
                    return [];
                }
                switch (keyword?.text ?? '') {
                    case 'restaurant brunch':
                        if (idx <= intervals.length / 2) {
                            return [
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                            ];
                        } else {
                            return [
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                            ];
                        }
                    case 'restaurant etoile':
                        if (idx <= intervals.length / 2) {
                            return [
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                            ];
                        } else {
                            return [
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                            ];
                        }
                    case 'restaurant cool':
                        if (idx <= intervals.length / 2) {
                            return [
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                            ];
                        } else {
                            return [
                                {
                                    name: 'Restaurant B',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                    vicinity: 'Paris 2',
                                },
                                {
                                    name: restaurant.name,
                                    place_id: restaurant.placeId,
                                    vicinity: 'osef',
                                },
                                {
                                    name: 'Restaurant C',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                    vicinity: 'Paris 3',
                                },
                                {
                                    name: 'Restaurant Z',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                    vicinity: 'Paris ZZ',
                                },
                                {
                                    name: 'Restaurant X',
                                    place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                    vicinity: 'Paris XX',
                                },
                            ];
                        }
                    default:
                        return [];
                }
            }
            // Arrange
            const testCaseBuilder = new TestCaseBuilder<'restaurants' | 'keywordsTemp' | 'restaurantKeywords' | 'geoSamples'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                // 48.87, 2.345 // 48.875, 2.345 // 48.875, 2.35 // 48.87, 2.35
                                getDefaultRestaurant()
                                    .name('Restau1')
                                    .placeId('placeid1')
                                    .latlng({ lat: 48.8745369, lng: 2.3452137 })
                                    .build(),
                                // 48.85, 2.375 // 48.85, 2.38 // 48.855, 2.375 // 48.855, 2.38
                                getDefaultRestaurant()
                                    .name('Restau2')
                                    .placeId('placeid2')
                                    .latlng({ lat: 48.8539839, lng: 2.3763766 })
                                    .build(),
                                // 48.875, 2.335 // 48.875, 2.34 // 48.88, 2.335 // 48.88, 2.34
                                getDefaultRestaurant()
                                    .name('Restau3')
                                    .placeId('placeid3')
                                    .latlng({ lat: 48.8780556, lng: 2.3394444 })
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('restaurant brunch').build(),
                                getDefaultKeywordTemp().text('restaurant etoile').build(),
                                getDefaultKeywordTemp().text('restaurant cool').build(),
                                getDefaultKeywordTemp().text('restaurant marrant').build(),
                                getDefaultKeywordTemp().text('restaurant grr baw').build(),
                                getDefaultKeywordTemp().text('restaurant toto').build(),
                                getDefaultKeywordTemp().text('restaurant titi').build(),
                                getDefaultKeywordTemp().text('restaurant tete').build(),
                                getDefaultKeywordTemp().text('restaurant tutu').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![1]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![2]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![3]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![4]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![5]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![6]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![7]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![8]._id)
                                    .build(),
                            ];
                        },
                    },
                    geoSamples: {
                        data(dependencies) {
                            const geoSamples = Interval.fromDateTimes(
                                DateTime.now().minus({ months: 2 }),
                                DateTime.now().plus({ weeks: 1 })
                            )
                                .splitBy({ weeks: 1 })
                                .map((interval, idx, dates) => {
                                    return (
                                        dependencies.restaurantKeywords?.map((restaurantKeyword) => {
                                            const keyword = dependencies.keywordsTemp?.find(
                                                (k) => k._id.toString() === restaurantKeyword._id.toString()
                                            );
                                            const restaurant = dependencies.restaurants?.find(
                                                (r) => r._id.toString() === restaurantKeyword.restaurantId.toString()
                                            );
                                            const angles = getContainingRectangle(restaurant!.latlng!.lat, restaurant!.latlng!.lng);
                                            return [
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tl.lat)
                                                    .lng(angles.tl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(buildRank(idx, dates, restaurant, keyword))
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tr.lat)
                                                    .lng(angles.tr.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(buildRank(idx, dates, restaurant, keyword))
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.bl.lat)
                                                    .lng(angles.bl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(buildRank(idx, dates, restaurant, keyword))
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.br.lat)
                                                    .lng(angles.br.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking(buildRank(idx, dates, restaurant, keyword))
                                                    .build(),
                                            ];
                                        }) ?? []
                                    );
                                });
                            return geoSamples.flat(2);
                        },
                    },
                },
                expectedResult(_deps) {
                    return ['restaurant etoile', 'restaurant brunch', 'restaurant cool'];
                },
            });
            await testCaseBuilder.build();
            const seeds = testCaseBuilder.getSeededObjects();
            const expected = testCaseBuilder.getExpectedResult();

            const restaurants = await restaurantRepo.find({
                filter: {
                    _id: { $in: seeds.restaurants?.map((r) => r._id) },
                },
            });

            const report = await createReport(restaurants, ReportType.MONTHLY_PERFORMANCE);

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceKeywordsSectionUseCase.getKeywordsEmailSection({
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            const keywordsTexts = emailSectionResult?.map((r) => r.keyword);
            expect(keywordsTexts).toMatchObject(expected);
        });

        it('should return empty result because the evolution is the same between the previous and current value', async () => {
            // Arrange
            const testCaseBuilder = new TestCaseBuilder<'restaurants' | 'keywordsTemp' | 'restaurantKeywords' | 'geoSamples'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                // 48.87, 2.345 // 48.875, 2.345 // 48.875, 2.35 // 48.87, 2.35
                                getDefaultRestaurant()
                                    .name('Restau1')
                                    .placeId('placeid1')
                                    .latlng({ lat: 48.8745369, lng: 2.3452137 })
                                    .build(),
                                // 48.85, 2.375 // 48.85, 2.38 // 48.855, 2.375 // 48.855, 2.38
                                getDefaultRestaurant()
                                    .name('Restau2')
                                    .placeId('placeid2')
                                    .latlng({ lat: 48.8539839, lng: 2.3763766 })
                                    .build(),
                                // 48.875, 2.335 // 48.875, 2.34 // 48.88, 2.335 // 48.88, 2.34
                                getDefaultRestaurant()
                                    .name('Restau3')
                                    .placeId('placeid3')
                                    .latlng({ lat: 48.8780556, lng: 2.3394444 })
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('restaurant brunch').build(),
                                getDefaultKeywordTemp().text('restaurant etoile').build(),
                                getDefaultKeywordTemp().text('restaurant cool').build(),
                                getDefaultKeywordTemp().text('restaurant marrant').build(),
                                getDefaultKeywordTemp().text('restaurant grr baw').build(),
                                getDefaultKeywordTemp().text('restaurant toto').build(),
                                getDefaultKeywordTemp().text('restaurant titi').build(),
                                getDefaultKeywordTemp().text('restaurant tete').build(),
                                getDefaultKeywordTemp().text('restaurant tutu').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![1]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![0]._id)
                                    .keywordId(dependencies.keywordsTemp![2]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![3]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![4]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![1]._id)
                                    .keywordId(dependencies.keywordsTemp![5]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![6]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![7]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants![2]._id)
                                    .keywordId(dependencies.keywordsTemp![8]._id)
                                    .build(),
                            ];
                        },
                    },
                    geoSamples: {
                        data(dependencies) {
                            const geoSamples = Interval.fromDateTimes(
                                DateTime.now().minus({ months: 2 }),
                                DateTime.now().plus({ weeks: 1 })
                            )
                                .splitBy({ weeks: 1 })
                                .map((interval) => {
                                    return (
                                        dependencies.restaurantKeywords?.map((restaurantKeyword) => {
                                            const keyword = dependencies.keywordsTemp?.find(
                                                (k) => k._id.toString() === restaurantKeyword._id.toString()
                                            );
                                            const restaurant = dependencies.restaurants?.find(
                                                (r) => r._id.toString() === restaurantKeyword.restaurantId.toString()
                                            );
                                            const angles = getContainingRectangle(restaurant!.latlng!.lat, restaurant!.latlng!.lng);
                                            return [
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tl.lat)
                                                    .lng(angles.tl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking([
                                                        {
                                                            name: 'Restaurant B',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                            vicinity: 'Paris 2',
                                                        },
                                                        {
                                                            name: restaurant?.name ?? '',
                                                            place_id: restaurant?.placeId ?? '',
                                                            vicinity: 'osef',
                                                        },
                                                        {
                                                            name: 'Restaurant C',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                            vicinity: 'Paris 3',
                                                        },
                                                        {
                                                            name: 'Restaurant Z',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                            vicinity: 'Paris ZZ',
                                                        },
                                                        {
                                                            name: 'Restaurant X',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                            vicinity: 'Paris XX',
                                                        },
                                                    ])
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.tr.lat)
                                                    .lng(angles.tr.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking([
                                                        {
                                                            name: 'Restaurant B',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                            vicinity: 'Paris 2',
                                                        },
                                                        {
                                                            name: restaurant?.name ?? '',
                                                            place_id: restaurant?.placeId ?? '',
                                                            vicinity: 'osef',
                                                        },
                                                        {
                                                            name: 'Restaurant C',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                            vicinity: 'Paris 3',
                                                        },
                                                        {
                                                            name: 'Restaurant Z',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                            vicinity: 'Paris ZZ',
                                                        },
                                                        {
                                                            name: 'Restaurant X',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                            vicinity: 'Paris XX',
                                                        },
                                                    ])
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.bl.lat)
                                                    .lng(angles.bl.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking([
                                                        {
                                                            name: 'Restaurant B',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                            vicinity: 'Paris 2',
                                                        },
                                                        {
                                                            name: restaurant?.name ?? '',
                                                            place_id: restaurant?.placeId ?? '',
                                                            vicinity: 'osef',
                                                        },
                                                        {
                                                            name: 'Restaurant C',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                            vicinity: 'Paris 3',
                                                        },
                                                        {
                                                            name: 'Restaurant Z',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                            vicinity: 'Paris ZZ',
                                                        },
                                                        {
                                                            name: 'Restaurant X',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                            vicinity: 'Paris XX',
                                                        },
                                                    ])
                                                    .build(),
                                                getDefaultGeoSample()
                                                    .keyword(keyword?.text ?? '')
                                                    .lat(angles.br.lat)
                                                    .lng(angles.br.lng)
                                                    .week(interval.start.weekNumber)
                                                    .year(interval.start.weekYear)
                                                    .createdAt(interval.start.toJSDate())
                                                    .ranking([
                                                        {
                                                            name: 'Restaurant B',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAB',
                                                            vicinity: 'Paris 2',
                                                        },
                                                        {
                                                            name: restaurant?.name ?? '',
                                                            place_id: restaurant?.placeId ?? '',
                                                            vicinity: 'osef',
                                                        },
                                                        {
                                                            name: 'Restaurant C',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAC',
                                                            vicinity: 'Paris 3',
                                                        },
                                                        {
                                                            name: 'Restaurant Z',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAE',
                                                            vicinity: 'Paris ZZ',
                                                        },
                                                        {
                                                            name: 'Restaurant X',
                                                            place_id: 'ChIJ3yn11Pxt5kcRjMyRkVFktAF',
                                                            vicinity: 'Paris XX',
                                                        },
                                                    ])
                                                    .build(),
                                            ];
                                        }) ?? []
                                    );
                                });
                            return geoSamples.flat(2);
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });
            await testCaseBuilder.build();
            const seeds = testCaseBuilder.getSeededObjects();

            const restaurants = await restaurantRepo.find({
                filter: {
                    _id: { $in: seeds.restaurants?.map((r) => r._id) },
                },
            });

            const report = await createReport(restaurants, ReportType.MONTHLY_PERFORMANCE);

            const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
            const endDate = DateTime.now().toJSDate();
            const emailSectionResult = await getPerformanceKeywordsSectionUseCase.execute({
                report,
                config: report.configurations[0],
                periods: { current: { startDate, endDate }, previous: { startDate, endDate } },
            });

            expect(emailSectionResult).toBeEmpty();
        });
    });

    describe.only('unsubscribing reports', () => {
        let reportRepository: ReportsRepository;
        let reportUseCases: ReportsUseCases;

        beforeAll(async () => {
            container.register(YextProvider, { useValue: new YextProviderMock() });
            reportRepository = container.resolve(ReportsRepository);
            reportUseCases = container.resolve(ReportsUseCases);
        });

        it('should unsubscribe the user', async () => {
            const testCaseBuilder = new TestCaseBuilder<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().email('<EMAIL>').build()];
                        },
                    },
                    reports: {
                        data(deps) {
                            return [
                                getDefaultReport()
                                    .userId(deps.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                            restaurantsIds: [],
                                        },
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', deps.users![0].email, '<EMAIL>'],
                                            restaurantsIds: [],
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCaseBuilder.build();
            const seeds = await testCaseBuilder.getSeededObjects();

            const report = seeds.reports?.[0] as IReport;
            const user = seeds.users?.[0] as IUser;
            const reportEntity = new Report({
                active: report.active,
                configurations: report.configurations.map((config) => ({
                    id: config._id.toString(),
                    recipients: config.recipients,
                    restaurantsIds: config.restaurantsIds.map((id) => id.toString()),
                    restaurants: [],
                })),
                createdAt: report.createdAt,
                id: report._id.toString(),
                type: report.type,
                updatedAt: report.updatedAt,
                userId: user._id.toString(),
                user: new User({
                    ...user,
                    organizationIds: user.organizationIds?.map((id) => id.toString()),
                    profilePicture: user.profilePicture?.toString(),
                    createdByUserId: user.createdByUserId?.toString(),
                    lastVisitedRestaurantId: user.lastVisitedRestaurantId?.toString(),
                    settings: {
                        ...user.settings,
                        receiveMessagesNotifications: {
                            ...user.settings.receiveMessagesNotifications,
                            restaurantsIds: user.settings.receiveMessagesNotifications.restaurantsIds.map((id) => id.toString()),
                        },
                        notifications: {
                            ...user.settings.notifications,
                            web: {
                                ...user.settings.notifications.web,
                                filters: {
                                    restaurantIds: user.settings.notifications.web.filters.restaurantIds.map((r) => r.toString()),
                                },
                            },
                        },
                    },
                }),
            });

            const hash = await digestMessage(`${reportEntity.id}${user.email}`, process.env.PASSPORT_SECRET);
            const index = 1;

            await reportUseCases.unsubscribeUserFromReport({
                reportId: report._id.toString(),
                email: user.email,
                hash,
                index,
            });

            const updatedReport = await reportRepository.findOne({
                filter: {
                    _id: report._id,
                },
            });

            const configuration = updatedReport?.configurations[index];

            expect(configuration?.recipients).not.toContain(user.email);
        });

        it('should throw error when trying to unsubscribe with different email', async () => {
            const testCaseBuilder = new TestCaseBuilder<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().email('<EMAIL>').build()];
                        },
                    },
                    reports: {
                        data(deps) {
                            return [
                                getDefaultReport()
                                    .userId(deps.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                            restaurantsIds: [],
                                        },
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', deps.users![0].email, '<EMAIL>'],
                                            restaurantsIds: [],
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCaseBuilder.build();
            const seeds = await testCaseBuilder.getSeededObjects();

            const report = seeds.reports?.[0] as IReport;
            const user = seeds.users?.[0] as IUser;
            const reportEntity = new Report({
                active: report.active,
                configurations: report.configurations.map((config) => ({
                    id: config._id.toString(),
                    recipients: config.recipients,
                    restaurantsIds: config.restaurantsIds.map((id) => id.toString()),
                    restaurants: [],
                })),
                createdAt: report.createdAt,
                id: report._id.toString(),
                type: report.type,
                updatedAt: report.updatedAt,
                userId: user._id.toString(),
                user: new User({
                    ...user,
                    organizationIds: user.organizationIds?.map((id) => id.toString()),
                    profilePicture: user.profilePicture?.toString(),
                    createdByUserId: user.createdByUserId?.toString(),
                    lastVisitedRestaurantId: user.lastVisitedRestaurantId?.toString(),
                    settings: {
                        ...user.settings,
                        receiveMessagesNotifications: {
                            ...user.settings.receiveMessagesNotifications,
                            restaurantsIds: user.settings.receiveMessagesNotifications.restaurantsIds.map((id) => id.toString()),
                        },
                        notifications: {
                            ...user.settings.notifications,
                            web: {
                                ...user.settings.notifications.web,
                                filters: {
                                    restaurantIds: user.settings.notifications.web.filters.restaurantIds.map((r) => r.toString()),
                                },
                            },
                        },
                    },
                }),
            });

            const hash = await digestMessage(`${reportEntity.id}${'<EMAIL>'}`, process.env.PASSPORT_SECRET);

            void expect(
                reportUseCases.unsubscribeUserFromReport({
                    reportId: report._id.toString(),
                    email: '<EMAIL>',
                    hash,
                    index: 1,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.INVALID_UNSUBSCRIBE_HASH,
                })
            );
        });

        it('should unsubscribe external user', async () => {
            const EXTERNAL_USER_EMAIL = '<EMAIL>';
            const testCaseBuilder = new TestCaseBuilder<'reports'>({
                seeds: {
                    reports: {
                        data() {
                            return [
                                getDefaultReport()
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                            restaurantsIds: [],
                                        },
                                        {
                                            _id: newDbId(),
                                            recipients: ['<EMAIL>', '<EMAIL>', EXTERNAL_USER_EMAIL],
                                            restaurantsIds: [],
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCaseBuilder.build();
            const seeds = await testCaseBuilder.getSeededObjects();

            const report = seeds.reports?.[0] as IReport;
            const reportEntity = new Report({
                active: report.active,
                configurations: report.configurations.map((config) => ({
                    id: config._id.toString(),
                    recipients: config.recipients,
                    restaurantsIds: config.restaurantsIds.map((id) => id.toString()),
                    restaurants: [],
                })),
                createdAt: report.createdAt,
                id: report._id.toString(),
                type: report.type,
                updatedAt: report.updatedAt,
                user: undefined,
                userId: newDbId().toString(),
            });

            const hash = await digestMessage(`${reportEntity.id}${EXTERNAL_USER_EMAIL}`, process.env.PASSPORT_SECRET);
            const index = 1;

            await reportUseCases.unsubscribeUserFromReport({
                reportId: report._id.toString(),
                email: EXTERNAL_USER_EMAIL,
                hash,
                index,
            });

            const updatedReport = await reportRepository.findOne({
                filter: {
                    _id: report._id,
                },
            });

            const configuration = updatedReport?.configurations[index];

            expect(configuration?.recipients).not.toContain(EXTERNAL_USER_EMAIL);
        });
    });
});
