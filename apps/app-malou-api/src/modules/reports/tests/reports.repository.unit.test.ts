import { omit } from 'lodash';
import { container } from 'tsyringe';

import { DbId, IRestaurant, newDbId } from '@malou-io/package-models';
import { ReportType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import ReportsRepository from ':modules/reports/reports.repository';
import { getDefaultReport } from ':modules/reports/tests/report.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';

const reportsRepository = container.resolve(ReportsRepository);

describe('reports.repository', () => {
    beforeAll(() => {
        registerRepositories(['UsersRepository', 'ReportsRepository', 'RestaurantsRepository']);
    });

    describe('removeUsersFromReports', () => {
        it('should remove reports and and user emails from configs when deleted - 1 user', async () => {
            const restaurantIds = [newDbId(), newDbId()];
            const testCase = new TestCaseBuilderV2<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .userId(dependencies.users()[0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[1].email],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users()[0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[2]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users()[0].email,
                                                dependencies.users()[1].email,
                                                dependencies.users()[2].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                // Other user's reports
                                getDefaultReport()
                                    .userId(dependencies.users()[1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[2]],
                                            recipients: [dependencies.users()[2].email, dependencies.users()[1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users()[0].email,
                                                dependencies.users()[1].email,
                                                dependencies.users()[2].email,
                                            ],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users()[1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[2].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users()[0].email, dependencies.users()[1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users()[0].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        reportIds: dependencies.reports.map((report) => report._id.toString()),
                    };
                },
            });

            await testCase.build();
            const { users } = testCase.getSeededObjects();
            const { reportIds } = testCase.getExpectedResult();

            await reportsRepository.removeUsersFromReports([users[0]._id.toString()]);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });
            expect(reports.length).toBe(2);

            // User's reports should be removed
            const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
            expect(report1).toBe(undefined);
            const report2 = reports.find((r) => r._id.toString() === reportIds[1]);
            expect(report2).toBe(undefined);

            // User should be removed from other reports
            const report3 = reports.find((r) => r._id.toString() === reportIds[3]);
            expect(report3?.configurations).toHaveLength(2); // 1 empty configuration should be removed
            expect(report3?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users[2].email],
                },
                {
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users[1].email],
                },
            ]);
        });

        it('should remove reports and and user emails from configs when deleted - several users', async () => {
            const restaurantIds = [newDbId(), newDbId()];
            const testCase = new TestCaseBuilder<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[2]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                                dependencies.users![3].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                // Other user's reports
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[2]],
                                            recipients: [dependencies.users![2].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                            ],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![2].email,
                                                dependencies.users![3].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                                // 3rd user
                                getDefaultReport()
                                    .userId(dependencies.users![2]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![2]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![2].email, dependencies.users![3].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![3].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![2]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                                dependencies.users![3].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                                dependencies.users![3].email,
                                            ],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![3].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        reportIds: dependencies.reports.map((report) => report._id.toString()),
                    };
                },
            });

            await testCase.build();
            const { users } = testCase.getSeededObjects();
            const { reportIds } = testCase.getExpectedResult();

            await reportsRepository.removeUsersFromReports([users![0]._id.toString(), users![1]._id.toString()]);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });
            expect(reports.length).toBe(3);

            // Users' reports should be removed
            const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
            expect(report1).toBe(undefined);
            const report2 = reports.find((r) => r._id.toString() === reportIds[1]);
            expect(report2).toBe(undefined);
            const report3 = reports.find((r) => r._id.toString() === reportIds[2]);
            expect(report3).toBe(undefined);
            const report4 = reports.find((r) => r._id.toString() === reportIds[3]);
            expect(report4).toBe(undefined);

            // Remaining reports
            const report5 = reports.find((r) => r._id.toString() === reportIds[4]);
            expect(report5?.configurations).toHaveLength(1); // 1 empty configuration should be removed
            expect(report5?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users?.[2].email],
                },
            ]);

            const report6 = reports.find((r) => r._id.toString() === reportIds[5]);
            expect(report6?.configurations).toHaveLength(2); // 1 empty configuration should be removed
            expect(report6?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users![2].email, users![3].email],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[3].email],
                },
            ]);

            const report7 = reports.find((r) => r._id.toString() === reportIds[6]);
            expect(report7?.configurations).toHaveLength(3); // 1 empty configuration should be removed
            expect(report7?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users?.[2].email, users?.[3].email],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[2].email, users?.[3].email],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[3].email],
                },
            ]);
        });
    });

    describe('removeRestaurantForUser', () => {
        it('should remove restaurantId and user emails from configs', async () => {
            const restaurantIds = [newDbId(), newDbId(), newDbId()];
            const testCase = new TestCaseBuilder<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[2]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                // Other user's reports
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[2]],
                                            recipients: [dependencies.users![2].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                            ],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return dependencies.reports.map((report) => report._id.toString());
                },
            });

            await testCase.build();
            const { users } = testCase.getSeededObjects();
            const reportsIds = testCase.getExpectedResult();

            await reportsRepository.removeRestaurantForUser(users?.[0]._id as DbId, restaurantIds[0] as DbId);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });
            const report1 = reports.find((r) => r._id.toString() === reportsIds[0]);
            expect(report1?.configurations).toHaveLength(1);
            expect(report1?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[0].email, users?.[1].email],
                })
            );
            const report2 = reports.find((r) => r._id.toString() === reportsIds[1]);
            expect(report2?.configurations).toHaveLength(2);
            expect(report2?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[0].email, users?.[1].email],
                })
            );
            expect(report2?.configurations[1]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[0].email, users?.[1].email],
                })
            );

            // Check that email has been removed from other user's report
            const report3 = reports.find((r) => r._id.toString() === reportsIds[2]);
            expect(report3?.configurations).toHaveLength(2);
            expect(report3?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[2].email, users?.[1].email],
                })
            );
            expect(report3?.configurations[1]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users?.[1].email, users?.[2].email],
                })
            );
        });

        it('should remove restaurantId and user emails from configs', async () => {
            const restaurantIds = [newDbId(), newDbId(), newDbId()];
            const testCase = new TestCaseBuilder<'users' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[2]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                // Other user's reports
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[2]],
                                            recipients: [dependencies.users![2].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                            ],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return dependencies.reports.map((report) => report._id.toString());
                },
            });

            await testCase.build();
            const { users } = testCase.getSeededObjects();
            const reportsIds = testCase.getExpectedResult();

            await reportsRepository.removeRestaurantForUser(users?.[1]._id as DbId, restaurantIds[0] as DbId);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });

            // Check that email has been removed from other user's report
            const report1 = reports.find((r) => r._id.toString() === reportsIds[0]);
            expect(report1?.configurations).toHaveLength(2);
            expect(report1?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users?.[0].email],
                })
            );
            const report2 = reports.find((r) => r._id.toString() === reportsIds[1]);
            expect(report2?.configurations).toHaveLength(3);
            expect(report2?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[0], restaurantIds[2]],
                    recipients: [users?.[0].email],
                })
            );
            expect(report2?.configurations[1]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users?.[0].email],
                })
            );
            expect(report2?.configurations[2]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[0].email],
                })
            );

            const report3 = reports.find((r) => r._id.toString() === reportsIds[2]);
            expect(report3?.configurations).toHaveLength(1);
            expect(report3?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[2].email, users?.[1].email],
                })
            );

            const report4 = reports.find((r) => r._id.toString() === reportsIds[3]);
            expect(report4?.configurations).toHaveLength(1);
            expect(report4?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[0].email, users?.[1].email],
                })
            );
        });
    });

    describe('addRestaurantForUser', () => {
        it('should add a configuration with restaurantId and user email to configs', async () => {
            const testCase = new TestCaseBuilder<'users' | 'restaurants' | 'reports'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                                getDefaultUser().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id, dependencies.restaurants![1]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![0]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![1]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                // Other user's reports
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![2]._id],
                                            recipients: [dependencies.users![2].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id],
                                            recipients: [
                                                dependencies.users![0].email,
                                                dependencies.users![1].email,
                                                dependencies.users![2].email,
                                            ],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .userId(dependencies.users![1]._id)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id, dependencies.restaurants![1]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [dependencies.restaurants![0]._id],
                                            recipients: [dependencies.users![0].email, dependencies.users![1].email],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        reportIds: dependencies.reports.map((report) => report._id.toString()),
                        restaurantIds: dependencies.restaurants.map((restaurant) => restaurant._id),
                    };
                },
            });

            await testCase.build();
            const { users, restaurants } = testCase.getSeededObjects();
            const { reportIds, restaurantIds } = testCase.getExpectedResult();

            await reportsRepository.addRestaurantForUser(users?.[0]._id as DbId, restaurants?.[2] as IRestaurant);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });

            // Check first report
            const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
            expect(report1?.configurations).toHaveLength(3);
            expect(report1?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                    recipients: [users?.[0].email, users?.[1].email],
                },
                {
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users?.[0].email, users?.[1].email],
                },
                {
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[0].email],
                },
            ]);
            const report2 = reports.find((r) => r._id.toString() === reportIds[1]);
            expect(report2?.configurations).toHaveLength(3);
            expect(report2?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users?.[0].email, users?.[1].email],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: [users?.[0].email, users?.[1].email],
                },
                {
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[0].email],
                },
            ]);

            // Check that other user's reports have not been changed
            const report3 = reports.find((r) => r._id.toString() === reportIds[2]);
            expect(report3?.configurations).toHaveLength(2);
            expect(report3?.configurations[0]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[2]],
                    recipients: [users?.[2].email, users?.[1].email],
                })
            );
            expect(report3?.configurations[1]).toEqual(
                expect.objectContaining({
                    restaurantsIds: [restaurantIds[0]],
                    recipients: [users?.[0].email, users?.[1].email, users?.[2].email],
                })
            );
        });
    });

    describe('removeEmptyConfigurations', () => {
        it('should remove configurations that have no restaurants of recipients', async () => {
            const testCase = new TestCaseBuilder<'reports'>({
                seeds: {
                    reports: {
                        data() {
                            return [
                                getDefaultReport()
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [newDbId(), newDbId()],
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [newDbId()],
                                            recipients: ['<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [],
                                            recipients: ['<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [newDbId()],
                                            recipients: [],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [newDbId(), newDbId()],
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [],
                                            recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [newDbId(), newDbId(), newDbId()],
                                            recipients: [],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        reportIds: dependencies.reports.map((report) => report._id.toString()),
                    };
                },
            });

            await testCase.build();
            const { reportIds } = testCase.getExpectedResult();

            await reportsRepository.removeEmptyConfigurations();

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });

            // Check first report
            const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
            expect(report1?.configurations).toHaveLength(2);
            const report2 = reports.find((r) => r._id.toString() === reportIds[1]);
            expect(report2?.configurations).toHaveLength(1);
        });
    });

    describe('removeRestaurantIdFromAllConfigurations', () => {
        it('should remove restaurantId from all configurations', async () => {
            const restaurantIds = [newDbId(), newDbId(), newDbId()];
            const testCase = new TestCaseBuilder<'reports'>({
                seeds: {
                    reports: {
                        data() {
                            return [
                                getDefaultReport()
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: ['<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[1]],
                                            recipients: ['<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: ['<EMAIL>'],
                                        },
                                    ])
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .build(),
                                getDefaultReport()
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: ['<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0]],
                                            recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                                        },
                                        {
                                            _id: newDbId(),
                                            restaurantsIds: [restaurantIds[0], restaurantIds[1]],
                                            recipients: ['<EMAIL>'],
                                        },
                                    ])
                                    .type(ReportType.WEEKLY_PERFORMANCE)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        reportIds: dependencies.reports.map((report) => report._id.toString()),
                    };
                },
            });

            await testCase.build();
            const { reportIds } = testCase.getExpectedResult();

            await reportsRepository.removeRestaurantIdFromAllConfigurations(restaurantIds[0] as DbId);

            // Get all reports
            const reports = await reportsRepository.find({
                filter: {},
                options: {
                    lean: true,
                },
            });

            // Check first report
            const report1 = reports.find((r) => r._id.toString() === reportIds[0]);
            expect(report1?.configurations).toHaveLength(3);
            expect(report1?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: ['<EMAIL>', '<EMAIL>'],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: ['<EMAIL>'],
                },
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: ['<EMAIL>'],
                },
            ]);
            const report2 = reports.find((r) => r._id.toString() === reportIds[1]);
            expect(report2?.configurations).toHaveLength(1);
            expect(report2?.configurations.map((config) => omit(config, '_id'))).toEqual([
                {
                    restaurantsIds: [restaurantIds[1]],
                    recipients: ['<EMAIL>'],
                },
            ]);
        });
    });
});
