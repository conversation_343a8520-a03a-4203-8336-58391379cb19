import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import { GetFacebookPagesQueryDto, getFacebookPagesQueryValidator, restaurantIdParamsValidator } from '@malou-io/package-dto';
import { EntityRepository, ICredential } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { Params, Query } from ':helpers/decorators/validators';
import { _getNewOrganizationIdsBuilder } from ':modules/credentials/credentials.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { CachePrefixKey, deleteCachePrefix } from ':plugins/cache-middleware';

import { FacebookCredentialsRepository } from './facebook.repository';
import * as facebookCredentialsUseCases from './facebook.use-cases';

@singleton()
export default class FacebookCredentialsController {
    constructor(
        private _facebookCredentialsRepository: FacebookCredentialsRepository,
        private _usersRepository: UsersRepository,
        private _restaurantsRepository: RestaurantsRepository,
        private _platformsRepository: PlatformsRepository
    ) {}

    handleOauthCallback = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { code, state } = req.query;

            // State added by GetOauthUrlService when generating login URL
            const { user: userId, location, platformKey, restaurantId } = JSON.parse(state as string);

            if (!code) {
                // user cancelled process
                return res.redirect(`${Config.baseAppUrl}`);
            }
            const user = await this._usersRepository.findOneOrFail({
                filter: { _id: userId },
                options: { populate: [{ path: 'organizations' }], lean: true },
            });

            if (!user.organizations?.length) {
                return res.redirect(`${Config.baseAppUrl}/credentials/missing_organization?redirect=${encodeURIComponent(location)}`);
            }
            const getNewOrganizationIds = _getNewOrganizationIdsBuilder(
                this._facebookCredentialsRepository as unknown as EntityRepository<ICredential>,
                this._platformsRepository,
                this._restaurantsRepository,
                PlatformKey.FACEBOOK
            );
            const handleOauthCallback = facebookCredentialsUseCases._handleOauthCallBackUseCase(
                facebookCredentialsUseCases,
                this._facebookCredentialsRepository as unknown as EntityRepository<ICredential>,
                getNewOrganizationIds
            );

            const updated = await handleOauthCallback({ user, restaurantId, code: code as string });
            const organizationId = user.organizations?.length === 1 ? user.organizations[0]?._id : null;
            if (updated) {
                const credentials = await this._facebookCredentialsRepository.find({ filter: { authId: updated.authId } });
                const credentialIds = credentials.map((e) => e._id);
                deleteCachePrefix(
                    CachePrefixKey.GET_PLATFORM_RESTAURANT_ACCOUNTS,
                    credentialIds.map((e) => [e.toString()])
                );
                return res.redirect(
                    // eslint-disable-next-line max-len
                    `${
                        Config.baseAppUrl
                        // eslint-disable-next-line max-len
                    }/credentials/validate?_id=${updated._id.toString()}&key=${platformKey}&should_choose_organization=${!organizationId}&redirect=${encodeURIComponent(
                        location
                    )}&auth_id=${updated.authId}`
                );
            }
            res.redirect(
                // eslint-disable-next-line max-len
                `${
                    Config.baseAppUrl
                }/credentials/validate?no_accounts=true&should_choose_organization=${!organizationId}&redirect=${encodeURIComponent(
                    location
                )}`
            );
        } catch (error) {
            next(error);
        }
    };

    handleGetIgMedias = async (req: Request, res: Response, next: NextFunction) => {
        const { restaurant_id: restaurantId } = req.params;
        const userId = req.user._id;
        return this._platformsRepository
            .findOneOrFail({ filter: { restaurantId, key: PlatformKey.INSTAGRAM }, options: { lean: true } })
            .then((platform) => facebookCredentialsUseCases.igGetPagePosts(userId, platform?.socialId ?? ''))
            .then((posts) => res.json({ data: posts }))
            .catch(next);
    };

    @Params(restaurantIdParamsValidator)
    async handleGetPermissions(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            const permissions = await facebookCredentialsUseCases.getUserPlatformsPermissions(restaurantId);
            res.status(200).json({ msg: 'facebook permissions', data: permissions });
        } catch (err) {
            next(err);
        }
    }

    @Query(getFacebookPagesQueryValidator)
    async handleGetPages(req: Request<any, any, any, GetFacebookPagesQueryDto>, res: Response, next: NextFunction) {
        try {
            const { q: query, onlyWithLocation, whitelistedPageIds, wantedPlatformSocialId } = req.query as any;
            const pagesData = await facebookCredentialsUseCases.pagesSearch(query, {
                onlyWithLocation,
                whitelistedPageIds: whitelistedPageIds ?? [],
                wantedPlatformSocialId,
            });
            return res.status(200).json({ msg: 'pages search data', data: pagesData });
        } catch (error) {
            next(error);
        }
    }
}
