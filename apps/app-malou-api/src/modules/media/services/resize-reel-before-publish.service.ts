import lodash from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPostWithAttachments, newDbId } from '@malou-io/package-models';
import { MediaCategory, MediaType } from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import { FormatterType, MediaFormatter } from ':modules/posts/services/formatters/media-formatter.port';

@singleton()
export class ResizeReelBeforePublishService {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _mediaUploaderService: MediaUploaderService
    ) {}

    async execute(post: IPostWithAttachments): Promise<{ mediaId: string }> {
        assert(post.restaurantId, 'https://maloufoodmarketing.slack.com/archives/C0115MRDS02/p1735837049198409');
        const media = new Media({
            id: post.attachments[0]._id.toString(),
            restaurantId: post.restaurantId.toString(),
            duplicatedFromRestaurantId: post.duplicatedFromRestaurantId?.toString(),
            userId: undefined,
            originalMediaId: undefined,
            postIds: post.attachments[0].postIds?.map((a) => a._id.toString()),
            ...post.attachments[0],
        } as any);
        const outputFilePath = `./downloadedMedias/${media.id}_resized.${media.getOriginalImageExtension()}`;
        const mediaFormatter = new MediaFormatter(media, FormatterType.REELS);
        assert(post.key);
        const resizedMediaMetadata = await mediaFormatter.formatMedia(
            media.urls.original,
            outputFilePath,
            post.key,
            media.type as MediaType
        );

        assert(media.dimensions);
        media.dimensions.original = { width: resizedMediaMetadata.width, height: resizedMediaMetadata.height };

        const id = newDbId().toString();
        const mediaToCreate = {
            ...lodash.omit(media, 'socialId'),
            id,
            socialId: id,
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
            postIds: media.postIds?.map((pid) => pid.toString()),
            originalMediaId: media.id?.toString(),
            category: media.category ?? MediaCategory.ADDITIONAL,
            createdAt: new Date(),
            updatedAt: new Date(),
            folderId: null,
        };
        const uploadedMedia = await this._mediaUploaderService.uploadFromFile(resizedMediaMetadata, mediaToCreate, {
            keepOriginalAsIgFit: true,
        });
        const createdMedia = await this._mediasRepository.createMedia(new Media({ ...mediaToCreate, ...uploadedMedia }));

        return { mediaId: createdMedia.id };
    }
}
