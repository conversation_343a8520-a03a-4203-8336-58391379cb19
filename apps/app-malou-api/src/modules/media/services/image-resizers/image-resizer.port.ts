import { autoInjectable, inject } from 'tsyringe';

import { SharpImageResizer } from './adapters/sharp-image-resizer.adapter';
import { CropOptions, FileMetadata, ImageResizerPort, OutputFileInfo, ResizeOptions } from './image-resizer.port.interface';

@autoInjectable()
export class ImageResizer implements ImageResizerPort {
    private _outputPath: string = '';

    constructor(@inject(SharpImageResizer) private _resizeAdapter: ImageResizerPort) {}
    resize(options: ResizeOptions): ImageResizerPort {
        return this._resizeAdapter.resize(options);
    }
    extract(options: CropOptions): ImageResizerPort {
        return this._resizeAdapter.extract(options);
    }
    toFile(outputPath: string): Promise<OutputFileInfo> {
        this._outputPath = outputPath;
        return this._resizeAdapter.toFile(outputPath);
    }
    initialize(filePath: string, outputPath: string): ImageResizerPort {
        return this._resizeAdapter.initialize(filePath, outputPath);
    }
    metadata(): Promise<FileMetadata> {
        return this._resizeAdapter.metadata();
    }

    rotate(degrees?: number): ImageResizerPort {
        return this._resizeAdapter.rotate(degrees);
    }

    getOutputPath(): string {
        return this._outputPath;
    }
}
