import { container } from 'tsyringe';

import { BrickDto } from '@malou-io/package-dto';
import { BricksCategory, BrickType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';

import { GetBricksFromBricksGeneratorUseCase } from '../use-cases/get-bricks-from-bricks-generator/get-bricks-from-bricks-generator.use-case';
import { getDefaultBrickGenerator } from './brick-generator.builder';
import { getDefaultBrick } from './brick.builder';

describe('GetBricksFromBricksGeneratorUseCase', () => {
    beforeAll(() => {
        registerRepositories(['BricksRepository', 'BricksGeneratorRepository']);
    });

    describe('execute', () => {
        it('should get bricks according to brick generators filtered by key and type', async () => {
            const getBricksFromBricksGeneratorUseCase = container.resolve(GetBricksFromBricksGeneratorUseCase);
            const key = 'key';
            const otherKey = 'otherKey';
            const type = BrickType.POSTAL_CODE_TO_TOURISTIC_AREA;
            const otherType = BrickType.CATEGORY_TO_SPECIAL;
            const brickId1 = 'brickId1';
            const brickId2 = 'brickId2';
            const brickId3 = 'brickId3';
            const brickId4 = 'brickId4';
            const brickId5 = 'brickId5';
            const brickId6 = 'brickId6';

            const testCase = new TestCaseBuilder<'bricks' | 'bricksgenerator'>({
                seeds: {
                    bricksgenerator: {
                        data() {
                            return [
                                getDefaultBrickGenerator().key(key).type(type).brickId(brickId1).build(),
                                getDefaultBrickGenerator().key(key).type(type).brickId(brickId2).build(),
                                getDefaultBrickGenerator().key(key).type(type).brickId(brickId3).build(),
                                getDefaultBrickGenerator().key(key).type(otherType).brickId(brickId4).build(),
                                getDefaultBrickGenerator().key(otherKey).type(type).brickId(brickId5).build(),
                                getDefaultBrickGenerator().key(otherKey).type(otherType).brickId(brickId6).build(),
                            ];
                        },
                    },
                    bricks: {
                        data() {
                            return [
                                getDefaultBrick().brickId(brickId1).category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId(brickId2).category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId(brickId3).category(BricksCategory.VENUE_LABEL).build(),
                                getDefaultBrick().brickId(brickId4).category(BricksCategory.TOURISTIC_AREA).build(),
                                getDefaultBrick().brickId(brickId5).category(BricksCategory.STATION).build(),
                                getDefaultBrick().brickId(brickId6).category(BricksCategory.VENUE_LOCATION).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): BrickDto[] {
                    const firstBrick: BrickDto = {
                        id: dependencies.bricks[0]._id.toString(),
                        brickId: dependencies.bricks[0].brickId,
                        category: dependencies.bricks[0].category,
                        key: dependencies.bricks[0].key,
                        text: dependencies.bricks[0].text,
                        subCategory: dependencies.bricks[0].subCategory,
                        needReview: dependencies.bricks[0].needReview,
                        nature: dependencies.bricks[0].nature,
                        gender: dependencies.bricks[0].gender,
                        number: dependencies.bricks[0].number,
                        currentLang: dependencies.bricks[0].currentLang,
                        meanSearchVol: dependencies.bricks[0].meanSearchVol,
                    };

                    const secondBrick: BrickDto = {
                        id: dependencies.bricks[1]._id.toString(),
                        brickId: dependencies.bricks[1].brickId,
                        category: dependencies.bricks[1].category,
                        key: dependencies.bricks[1].key,
                        text: dependencies.bricks[1].text,
                        subCategory: dependencies.bricks[1].subCategory,
                        needReview: dependencies.bricks[1].needReview,
                        nature: dependencies.bricks[1].nature,
                        gender: dependencies.bricks[1].gender,
                        number: dependencies.bricks[1].number,
                        currentLang: dependencies.bricks[1].currentLang,
                        meanSearchVol: dependencies.bricks[1].meanSearchVol,
                    };

                    const thirdBrick: BrickDto = {
                        id: dependencies.bricks[2]._id.toString(),
                        brickId: dependencies.bricks[2].brickId,
                        category: dependencies.bricks[2].category,
                        key: dependencies.bricks[2].key,
                        text: dependencies.bricks[2].text,
                        subCategory: dependencies.bricks[2].subCategory,
                        needReview: dependencies.bricks[2].needReview,
                        nature: dependencies.bricks[2].nature,
                        gender: dependencies.bricks[2].gender,
                        number: dependencies.bricks[2].number,
                        currentLang: dependencies.bricks[2].currentLang,
                        meanSearchVol: dependencies.bricks[2].meanSearchVol,
                    };

                    return [firstBrick, secondBrick, thirdBrick];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const result = await getBricksFromBricksGeneratorUseCase.execute(key, type);

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});
