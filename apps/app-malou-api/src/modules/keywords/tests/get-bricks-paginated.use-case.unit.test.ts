import { container } from 'tsyringe';

import { BrickDto } from '@malou-io/package-dto';
import { BricksCategory } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import { getDefaultBrick } from ':modules/keywords/tests/brick.builder';
import { GetBricksPaginatedUseCase } from ':modules/keywords/use-cases/get-bricks-paginated/get-bricks-paginated.use-case';

describe('GetBricksPaginatedUseCase', () => {
    beforeAll(() => {
        registerRepositories(['BricksRepository']);
    });

    describe('execute', () => {
        it('should get all bricks except those about location', async () => {
            const getBricksPaginatedUseCase = container.resolve(GetBricksPaginatedUseCase);

            const testCase = new TestCaseBuilder<'bricks'>({
                seeds: {
                    bricks: {
                        data() {
                            return [
                                getDefaultBrick().brickId('brickCategoryId').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId').category(BricksCategory.VENUE_LABEL).build(),
                                getDefaultBrick().brickId('brickTouristicAreaId').category(BricksCategory.TOURISTIC_AREA).build(),
                                getDefaultBrick().brickId('brickStationId').category(BricksCategory.STATION).build(),
                                getDefaultBrick().brickId('brickLocationId').category(BricksCategory.VENUE_LOCATION).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): BrickDto[] {
                    const firstBrick: BrickDto = {
                        id: dependencies.bricks[0]._id.toString(),
                        brickId: dependencies.bricks[0].brickId,
                        category: dependencies.bricks[0].category,
                        key: dependencies.bricks[0].key,
                        text: dependencies.bricks[0].text,
                        subCategory: dependencies.bricks[0].subCategory,
                        needReview: dependencies.bricks[0].needReview,
                        nature: dependencies.bricks[0].nature,
                        gender: dependencies.bricks[0].gender,
                        number: dependencies.bricks[0].number,
                        currentLang: dependencies.bricks[0].currentLang,
                        meanSearchVol: dependencies.bricks[0].meanSearchVol,
                    };

                    const secondBrick: BrickDto = {
                        id: dependencies.bricks[1]._id.toString(),
                        brickId: dependencies.bricks[1].brickId,
                        category: dependencies.bricks[1].category,
                        key: dependencies.bricks[1].key,
                        text: dependencies.bricks[1].text,
                        subCategory: dependencies.bricks[1].subCategory,
                        needReview: dependencies.bricks[1].needReview,
                        nature: dependencies.bricks[1].nature,
                        gender: dependencies.bricks[1].gender,
                        number: dependencies.bricks[1].number,
                        currentLang: dependencies.bricks[1].currentLang,
                        meanSearchVol: dependencies.bricks[1].meanSearchVol,
                    };

                    const thirdBrick: BrickDto = {
                        id: dependencies.bricks[2]._id.toString(),
                        brickId: dependencies.bricks[2].brickId,
                        category: dependencies.bricks[2].category,
                        key: dependencies.bricks[2].key,
                        text: dependencies.bricks[2].text,
                        subCategory: dependencies.bricks[2].subCategory,
                        needReview: dependencies.bricks[2].needReview,
                        nature: dependencies.bricks[2].nature,
                        gender: dependencies.bricks[2].gender,
                        number: dependencies.bricks[2].number,
                        currentLang: dependencies.bricks[2].currentLang,
                        meanSearchVol: dependencies.bricks[2].meanSearchVol,
                    };

                    return [firstBrick, secondBrick, thirdBrick];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const pageNumber = undefined;
            const pageSize = undefined;
            const total = undefined;
            const result = await getBricksPaginatedUseCase.execute({ pageNumber, pageSize, total });

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should get all bricks except those about location according to the page size', async () => {
            const getBricksPaginatedUseCase = container.resolve(GetBricksPaginatedUseCase);

            const testCase = new TestCaseBuilder<'bricks'>({
                seeds: {
                    bricks: {
                        data() {
                            return [
                                getDefaultBrick().brickId('brickCategoryId').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId').category(BricksCategory.VENUE_LABEL).build(),
                                getDefaultBrick().brickId('brickTouristicAreaId').category(BricksCategory.TOURISTIC_AREA).build(),
                                getDefaultBrick().brickId('brickStationId').category(BricksCategory.STATION).build(),
                                getDefaultBrick().brickId('brickLocationId').category(BricksCategory.VENUE_LOCATION).build(),
                                getDefaultBrick().brickId('brickCategoryId2').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId2').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId2').category(BricksCategory.VENUE_LABEL).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): BrickDto[] {
                    const firstBrick: BrickDto = {
                        id: dependencies.bricks[0]._id.toString(),
                        brickId: dependencies.bricks[0].brickId,
                        category: dependencies.bricks[0].category,
                        key: dependencies.bricks[0].key,
                        text: dependencies.bricks[0].text,
                        subCategory: dependencies.bricks[0].subCategory,
                        needReview: dependencies.bricks[0].needReview,
                        nature: dependencies.bricks[0].nature,
                        gender: dependencies.bricks[0].gender,
                        number: dependencies.bricks[0].number,
                        currentLang: dependencies.bricks[0].currentLang,
                        meanSearchVol: dependencies.bricks[0].meanSearchVol,
                    };

                    const secondBrick: BrickDto = {
                        id: dependencies.bricks[1]._id.toString(),
                        brickId: dependencies.bricks[1].brickId,
                        category: dependencies.bricks[1].category,
                        key: dependencies.bricks[1].key,
                        text: dependencies.bricks[1].text,
                        subCategory: dependencies.bricks[1].subCategory,
                        needReview: dependencies.bricks[1].needReview,
                        nature: dependencies.bricks[1].nature,
                        gender: dependencies.bricks[1].gender,
                        number: dependencies.bricks[1].number,
                        currentLang: dependencies.bricks[1].currentLang,
                        meanSearchVol: dependencies.bricks[1].meanSearchVol,
                    };

                    const thirdBrick: BrickDto = {
                        id: dependencies.bricks[2]._id.toString(),
                        brickId: dependencies.bricks[2].brickId,
                        category: dependencies.bricks[2].category,
                        key: dependencies.bricks[2].key,
                        text: dependencies.bricks[2].text,
                        subCategory: dependencies.bricks[2].subCategory,
                        needReview: dependencies.bricks[2].needReview,
                        nature: dependencies.bricks[2].nature,
                        gender: dependencies.bricks[2].gender,
                        number: dependencies.bricks[2].number,
                        currentLang: dependencies.bricks[2].currentLang,
                        meanSearchVol: dependencies.bricks[2].meanSearchVol,
                    };

                    return [firstBrick, secondBrick, thirdBrick];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const pageNumber = 0;
            const pageSize = 3;
            const total = undefined;
            const result = await getBricksPaginatedUseCase.execute({ pageNumber, pageSize, total });

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should get all bricks except those about location according to the page number', async () => {
            const getBricksPaginatedUseCase = container.resolve(GetBricksPaginatedUseCase);

            const testCase = new TestCaseBuilder<'bricks'>({
                seeds: {
                    bricks: {
                        data() {
                            return [
                                getDefaultBrick().brickId('brickCategoryId').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId').category(BricksCategory.VENUE_LABEL).build(),
                                getDefaultBrick().brickId('brickTouristicAreaId').category(BricksCategory.TOURISTIC_AREA).build(),
                                getDefaultBrick().brickId('brickStationId').category(BricksCategory.STATION).build(),
                                getDefaultBrick().brickId('brickLocationId').category(BricksCategory.VENUE_LOCATION).build(),
                                getDefaultBrick().brickId('brickCategoryId2').category(BricksCategory.VENUE_TYPE).build(),
                                getDefaultBrick().brickId('brickSpecialsId2').category(BricksCategory.VENUE_SPECIAL).build(),
                                getDefaultBrick().brickId('brickLabelsId2').category(BricksCategory.VENUE_LABEL).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): BrickDto[] {
                    const firstBrick: BrickDto = {
                        id: dependencies.bricks[6]._id.toString(),
                        brickId: dependencies.bricks[6].brickId,
                        category: dependencies.bricks[6].category,
                        key: dependencies.bricks[6].key,
                        text: dependencies.bricks[6].text,
                        subCategory: dependencies.bricks[6].subCategory,
                        needReview: dependencies.bricks[6].needReview,
                        nature: dependencies.bricks[6].nature,
                        gender: dependencies.bricks[6].gender,
                        number: dependencies.bricks[6].number,
                        currentLang: dependencies.bricks[6].currentLang,
                        meanSearchVol: dependencies.bricks[6].meanSearchVol,
                    };

                    const secondBrick: BrickDto = {
                        id: dependencies.bricks[7]._id.toString(),
                        brickId: dependencies.bricks[7].brickId,
                        category: dependencies.bricks[7].category,
                        key: dependencies.bricks[7].key,
                        text: dependencies.bricks[7].text,
                        subCategory: dependencies.bricks[7].subCategory,
                        needReview: dependencies.bricks[7].needReview,
                        nature: dependencies.bricks[7].nature,
                        gender: dependencies.bricks[7].gender,
                        number: dependencies.bricks[7].number,
                        currentLang: dependencies.bricks[7].currentLang,
                        meanSearchVol: dependencies.bricks[7].meanSearchVol,
                    };

                    const thirdBrick: BrickDto = {
                        id: dependencies.bricks[8]._id.toString(),
                        brickId: dependencies.bricks[8].brickId,
                        category: dependencies.bricks[8].category,
                        key: dependencies.bricks[8].key,
                        text: dependencies.bricks[8].text,
                        subCategory: dependencies.bricks[8].subCategory,
                        needReview: dependencies.bricks[8].needReview,
                        nature: dependencies.bricks[8].nature,
                        gender: dependencies.bricks[8].gender,
                        number: dependencies.bricks[8].number,
                        currentLang: dependencies.bricks[8].currentLang,
                        meanSearchVol: dependencies.bricks[8].meanSearchVol,
                    };

                    return [firstBrick, secondBrick, thirdBrick];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const pageNumber = 1;
            const pageSize = 3;
            const total = undefined;
            const result = await getBricksPaginatedUseCase.execute({ pageNumber, pageSize, total });

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});
