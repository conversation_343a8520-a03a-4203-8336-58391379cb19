import * as Sentry from '@sentry/node';
import * as lodash from 'lodash';
import { DateTime } from 'luxon';
import { AnyBulkWriteOperation } from 'mongodb';
import { UpdateAggregationStage } from 'mongoose';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';
import z from 'zod';

import {
    DbId,
    GeoSampleModel,
    IGeoSample,
    IKeywordTemp,
    IMapsPlaceInfo,
    IRestaurant,
    IRestaurantKeyword,
    MapsPlaceInfoModel,
} from '@malou-io/package-models';
import {
    CcTld,
    ccTldByCountryCode,
    GeoSamplePlatform,
    GMapsApiVersion,
    ILatlng,
    MalouErrorCode,
    TimeInSeconds,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { getDistanceFromLatLonInKm } from ':helpers/utils';
import GeoSampleRepository from ':modules/keywords/modules/geolocation/geolocation.repository';
import {
    getContainingRectangle,
    getWeeksAndYearsFromDatesInterval,
    globalGridIncrement,
} from ':modules/keywords/modules/geolocation/utils';
import { SearchQuery } from ':modules/keywords/modules/keywords.interface';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { Cache } from ':plugins/cache';
import { getRankingForKeywordAndLatLng, GmapsPlaceDto, performNewPlaceDetails, performNewTextSearch } from ':plugins/gmaps';
import { metricsService } from ':services/metrics.service';

import { KeywordsTempRepository } from '../keywords-temp.repository';
import { MapsPlaceInfoRepository } from '../maps-place-info.repository';
import { DateTimeIntervalType } from '../modules/geolocation/geolocation.interface';
import { WeeklySearchRankingRepository as WeeklySearchRankingsRepository } from '../weekly-search-rankings.repository';

/**
 * For a given search, Google Maps gives us a list of ordered search results.
 * We assign a score to each result. The first results have a high score, the last
 * ones have a smaller score. Scores are decreasing a bit exponentially because it’s
 * much more beneficial for a restaurant to go from the second place to the first place
 * than it is to go from the 19th place to the 18th.
 *
 * rankScores[0] is the score assigned to the first search result (50), rankScores[1]
 * is for the second result (43), rankScores[2] for the third (37), etc.
 */
const RANK_SCORES = [50, 43, 37, 31, 27, 23, 19, 17, 15, 13, 11, 9, 8, 7, 6, 5, 4, 3, 2, 1];

type GeoSampleWithoutIdAndDbDates = Omit<IGeoSample, '_id' | 'createdAt' | 'updatedAt'>;

const upsertedCounter = metricsService.getMeter().createCounter('geosamples.upserted', {
    description: 'Counter of upserted geosamples',
});

const updatedCounter = metricsService.getMeter().createCounter('geosamples.updated', {
    description: 'Counter of updated geosamples',
});

const weeklySearchRankingUpsertedCounter = metricsService.getMeter().createCounter('weeklysearchrankings.upserted', {
    description: 'Counter of upserted documents on the collection weeklysearchrankings',
});

const fetchRecentSamplesCounter = metricsService.getMeter().createCounter('geosamples.fetchRecentSamples', {
    description: 'Counter of calls to fetchRecentSamples',
});

const upsertDurationHistogram = metricsService.getMeter().createHistogram('geosamples.upsertDuration', {
    description: 'The duration that upsertGeosamples() spends to actually insert geosamples',
    advice: { explicitBucketBoundaries: [0.1, 0.25, 0.5, 1, 2, 4] },
    unit: 'seconds',
});

const wsrBulkRefreshDuration = metricsService.getMeter().createHistogram('geosamples.weeklySearchRankingsRefreshDuration', {
    description: 'The duration that upsertGeosamples() spends to refresh the weeklysearchrankings collection',
    advice: { explicitBucketBoundaries: [0.5, 1, 2, 4, 8, 16] },
    unit: 'seconds',
});

const googleMapsResultValidator = z.object({
    name: z.string().nonempty(),
    geometry: z.object({
        location: z.object({ lat: z.number(), lng: z.number() }),
    }),
    place_id: z.string().nonempty(),
});

@singleton()
export class GeoSampleService {
    constructor(
        private readonly _geoSamplesRepository: GeoSampleRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _mapsPlaceInfoRepository: MapsPlaceInfoRepository,
        private readonly _weeklySearchRankingsRepository: WeeklySearchRankingsRepository,
        private readonly _keywordsRepository: KeywordsTempRepository,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    /** The only function that inserts geosamples in the database. */
    async upsertGeosamples(geosamples: GeoSampleWithoutIdAndDbDates[]): Promise<void> {
        assert(
            geosamples.every((s) => s.region),
            'new geosamples must have a region'
        );

        const begin = +new Date();
        const results = await this._geoSamplesRepository.bulkOperations({
            operations: geosamples.map((rawSample): AnyBulkWriteOperation => {
                const doc = new GeoSampleModel(rawSample);
                const validationError = doc.validateSync();
                if (validationError) {
                    logger.error('[upsertGeosamples] invalid geosample', { rawSample, validationError });
                    throw validationError;
                }
                const sample: Omit<IGeoSample, '_id'> & { _id?: DbId; id?: string } = doc.toObject();
                delete sample._id;
                delete sample.id;
                return {
                    replaceOne: {
                        // the filter matches the fields of the unique index declared in geo-sample-model.ts
                        filter: {
                            lat: sample.lat,
                            lng: sample.lng,
                            keyword: sample.keyword,
                            platformKey: sample.platformKey,
                            week: sample.week,
                            year: sample.year,
                        },
                        replacement: sample,
                        upsert: true,
                    },
                };
            }),
            options: { ordered: false },
        });
        upsertedCounter.add(results.upsertedCount);
        updatedCounter.add(results.modifiedCount);
        upsertDurationHistogram.record((+new Date() - begin) / 1000);

        if (results.result.writeErrors.length) {
            logger.error('[upsertGeosamples] errors', results.result.writeErrors);
        }

        await this._insertMapsPlaceInfoFromGeosamples(geosamples);
        await this._refreshWeeklySearchRankingsFromGeosamples(geosamples);
    }

    /**
     * Refreshes recent samples.
     *
     * If the Google Map API is not available for some reason, this function does not throw
     * and creates geosamples with `error: true` instead.
     */
    async fetchRecentSamples({
        keywordsWithRegion,
        latlngs,
        placeId,
    }: {
        keywordsWithRegion: { keyword: string; region: CcTld }[];
        latlngs: ILatlng[];
        placeId: string;
    }): Promise<void> {
        fetchRecentSamplesCounter.add(1);

        const today = DateTime.now();
        const { weekNumber: week, weekYear: year } = today;
        const keywords = keywordsWithRegion.map((keywordWithRegion) => keywordWithRegion.keyword);

        const todaysSamples = await this.getRectangleGeoSamplesForLatAndLngAndKeywords({
            latlngs,
            keywords,
            years: [year],
            weeks: [week],
        });

        const gMapsApiVersion = await this._getGMapsApiVersion(placeId);

        // We will compare search queries with lodash.isEqual so we have to make sure no
        // extra field is present:
        const toPlainSearchQuery = (q: SearchQuery): SearchQuery => ({
            lat: q.lat,
            lng: q.lng,
            keyword: q.keyword,
            platformKey: q.platformKey,
            region: q.region,
            version: q.version,
        });

        // All the queries we have searched today.
        const todaysSamplesQueries: SearchQuery[] = todaysSamples.map((s): SearchQuery => {
            const region = keywordsWithRegion.find((keywordWithRegion) => keywordWithRegion.keyword === s.keyword)?.region;
            assert(region, 'recent geosamples must have a valid region');
            return toPlainSearchQuery({ ...s, region, version: gMapsApiVersion });
        });

        const wantedQueries = this.generateWantedSearchQueries({ keywordsWithRegion, latlngs, gMapsApiVersion }).map(toPlainSearchQuery);

        const todaysMissingQueries = lodash.differenceWith(wantedQueries, todaysSamplesQueries, lodash.isEqual);

        if (todaysMissingQueries.length > 0) {
            const geosamples = await this.fetchKeywordsForLatsLngs(todaysMissingQueries);
            await this.upsertGeosamples(geosamples);
        }
    }

    /**
     * Issues search queries on Google Maps and returns results.
     *
     * If the Google Map API is not available for some reason, this function does not throw
     * and returns geosamples with `error: true` instead.
     */
    async fetchKeywordsForLatsLngs(queries: SearchQuery[]): Promise<GeoSampleWithoutIdAndDbDates[]> {
        const today = DateTime.now();
        const { weekNumber: week, weekYear: year } = today;

        let results: GeoSampleWithoutIdAndDbDates[] = [];
        let samplesToFetchChunk: SearchQuery[];
        const chunkSize = 10;

        // Split queries into two lists
        const v1Queries: SearchQuery[] = [];
        const v2Queries: SearchQuery[] = [];

        for (const query of queries) {
            if (query.version === GMapsApiVersion.V1) {
                v1Queries.push(query);
            } else {
                v2Queries.push(query);
            }
        }

        logger.info('[update weekly geosamples] [V1]', v1Queries);
        logger.info('[update weekly geosamples] [V2]', v2Queries);

        // V1
        for (let i = 0, j = v1Queries.length; i < j; i += chunkSize) {
            const v1Promises: Promise<GeoSampleWithoutIdAndDbDates>[] = [];
            samplesToFetchChunk = v1Queries.slice(i, i + chunkSize);
            if (samplesToFetchChunk.length) {
                for (const sampleToFetch of samplesToFetchChunk) {
                    v1Promises.push(
                        this.fetchGeoSampleFromPlatform(sampleToFetch)
                            .then((ranking) => {
                                const geoSampleDoc: GeoSampleWithoutIdAndDbDates = {
                                    ...sampleToFetch,
                                    week,
                                    year,
                                    ranking,
                                };
                                if (!ranking?.length) {
                                    return { ...geoSampleDoc, ranking: [] };
                                }
                                const {
                                    location: { lat: bestResultLat, lng: bestResultLng },
                                } = ranking?.[0]?.geometry;
                                const bestResultDistanceToSample = getDistanceFromLatLonInKm(
                                    bestResultLat,
                                    bestResultLng,
                                    sampleToFetch.lat,
                                    sampleToFetch.lng
                                );
                                const MAX_DISTANCE_TO_SAMPLE_IN_KM = 50;
                                if (bestResultDistanceToSample > MAX_DISTANCE_TO_SAMPLE_IN_KM) {
                                    geoSampleDoc.errorData = 'results_too_far';
                                    geoSampleDoc.error = true;
                                }
                                return { ...geoSampleDoc, ranking };
                            })
                            .catch((e) => ({
                                ...sampleToFetch,
                                week,
                                year,
                                ranking: [],
                                error: true,
                                errorData: e?.message ?? null,
                            }))
                    );
                }
            }
            const samplesToFetchChunkResults = await Promise.all(v1Promises);
            results = results.concat(samplesToFetchChunkResults);
        }

        // V2
        const v2Results: { placeIds: string[]; lat: number; lng: number; keyword: string; region: string }[] = [];

        for (let i = 0; i < v2Queries.length; i += chunkSize) {
            const promises = v2Queries.slice(i, i + chunkSize).map(async (sampleToFetch) => {
                try {
                    const result = await performNewTextSearch({
                        textQuery: sampleToFetch.keyword,
                        locationBias: {
                            circle: {
                                center: { latitude: sampleToFetch.lat, longitude: sampleToFetch.lng },
                                radius: +Config.geolocation.keywordRadiusSearchMeters,
                            },
                        },
                        regionCode: sampleToFetch.region,
                    });

                    logger.info('[GeoSampleService] [new gmaps search api] reply', result);

                    if (result.success) {
                        v2Results.push({
                            lat: sampleToFetch.lat,
                            lng: sampleToFetch.lng,
                            keyword: sampleToFetch.keyword,
                            region: sampleToFetch.region,
                            placeIds: result.placeIds,
                        });
                    }
                } catch (error: any) {
                    logger.info('[GeoSampleService] [new gmaps search api] thrown error', error.stack);
                    return {
                        ...sampleToFetch,
                        week,
                        year,
                        ranking: [],
                        error: true,
                        errorData: error?.message ?? null,
                    };
                }
            });

            await Promise.all(promises);
        }

        const allPlaceIds = v2Results.flatMap((r) => r.placeIds);
        await this._ensurePlaceDetailsAreSaved(allPlaceIds);
        const allPlaces = await this._mapsPlaceInfoRepository.find({
            filter: { platformKey: GeoSamplePlatform.GMAPS, placeId: { $in: allPlaceIds } },
            options: { lean: true },
        });
        const allPlacesByPlaceId = new Map(allPlaces.map((p) => [p.placeId, p]));

        for (const result of v2Results) {
            const places = result.placeIds.map((placeId): IMapsPlaceInfo => {
                const place = allPlacesByPlaceId.get(placeId);
                assert(place);
                return place;
            });

            const geoSample: GeoSampleWithoutIdAndDbDates = {
                lat: result.lat,
                lng: result.lng,
                keyword: result.keyword,
                platformKey: GeoSamplePlatform.GMAPS,
                week: today.weekNumber,
                year: today.weekYear,
                ranking: places.map((place) => ({
                    formatted_address: place.shortAddressText,
                    geometry: {
                        location: { lat: place.lat, lng: place.lng },
                        viewport: {
                            northeast: { lat: place.lat, lng: place.lng },
                            southwest: { lat: place.lat, lng: place.lng },
                        },
                    },
                    name: place.name,
                    place_id: place.placeId,
                })),
                region: result.region,
            };

            if (places.length >= 1) {
                const FAR_RESULT_IN_KM = 50;
                const bestResultDistanceToSample = getDistanceFromLatLonInKm(places[0].lat, places[0].lng, result.lat, result.lng);
                if (bestResultDistanceToSample > FAR_RESULT_IN_KM) {
                    geoSample.errorData = 'results_too_far';
                    geoSample.error = true;
                }
            }

            results.push(geoSample);
        }
        return results;
    }

    public generateWantedSearchQueries = ({
        keywordsWithRegion,
        latlngs,
        gMapsApiVersion,
    }: {
        keywordsWithRegion: { keyword: string; region: CcTld }[];
        latlngs: ILatlng[];
        gMapsApiVersion: GMapsApiVersion;
    }): SearchQuery[] => {
        const rectangles = latlngs.map((latlng) => getContainingRectangle(latlng.lat, latlng.lng));
        const vertices: ILatlng[] = lodash.uniqWith(
            rectangles.flatMap((r) => [r.tl, r.tr, r.br, r.bl]),
            (a, b) => a.lat === b.lat && a.lng === b.lng
        );

        return Object.values(GeoSamplePlatform).flatMap((platformKey) =>
            keywordsWithRegion.flatMap((keywordWithRegion) =>
                vertices.flatMap((vertex) => ({
                    lat: vertex.lat,
                    lng: vertex.lng,
                    keyword: keywordWithRegion.keyword,
                    platformKey,
                    region: keywordWithRegion.region,
                    version: gMapsApiVersion,
                }))
            )
        );
    };

    public async getRectangleGeoSamplesForLatAndLngAndKeywords({
        latlngs,
        keywords,
        years,
        weeks,
        startDate,
        endDate,
    }: {
        latlngs: ILatlng[];
        keywords?: string[];
        years?: number[];
        weeks?: number[];
        startDate?: Date;
        endDate?: Date;
    }): Promise<IGeoSample[]> {
        const rectangles = latlngs.map((latlng) => getContainingRectangle(latlng.lat, latlng.lng));
        const verticesLatLng: ILatlng[] = lodash.uniqWith(
            rectangles.flatMap((r) => [r.tl, r.tr, r.br, r.bl]),
            (a, b) => a.lat === b.lat && a.lng === b.lng
        );

        return this.findGeoSamplesFromDB({
            verticesLatLng,
            keywords,
            platformKeys: undefined,
            years,
            weeks,
            startDate,
            endDate,
        });
    }

    /**
     * Generates weekly SEO statistics for each restaurant.
     *
     * This function performs a lot of searches on the Google Maps API and saves each search
     * result in our database.
     *
     * Returns true if there are more queries to perform in the next run of the cron job.
     */
    public async fetchGmapsWeeklyGeoSamples(): Promise<boolean> {
        if (this._cache.status() !== 'ready') {
            logger.info('[AGENDA_JOB] [update weekly geosamples] Redis not connected');
            return true;
        }

        let weekSamples = (await this._cache.get('geosamples:weekly-samples:list')) as string;

        if (!weekSamples) {
            const sampleList = await this._getWeeklyGmapsSearchQueries();
            weekSamples = JSON.stringify(sampleList);
            const chunkSize = Math.floor(sampleList.length / (60 * 3)) + 1; // job runs every minute, so we split the total samples in a 3hours run
            await this._cache.set('geosamples:weekly-samples:list', weekSamples, 7 * TimeInSeconds.HOUR);
            await this._cache.set('geosamples:weekly-samples:chunk-size', chunkSize, 7 * TimeInSeconds.HOUR);
            await this._cache.set('geosamples:weekly-samples:offset', 0, 7 * TimeInSeconds.HOUR);
        }

        const allQueries = JSON.parse(weekSamples);
        const offset = parseInt((await this._cache.get('geosamples:weekly-samples:offset')) as string, 10);
        const chunkSize = parseInt((await this._cache.get('geosamples:weekly-samples:chunk-size')) as string, 10);
        logger.info(`[AGENDA_JOB] [update weekly geosamples] - Will process samples`, {
            samplesCount: allQueries.length,
            chunkSize,
        });

        const chunk = allQueries.slice(offset, chunkSize + offset);
        if (chunk.length === 0) {
            return false;
        }

        // TODO check how we can propagate the error better (do we need all these try / catch ?)
        try {
            await this._processWeeklySamplesChunk(offset, chunk);
        } catch (err) {
            Sentry.captureException(err);
            logger.error('[AGENDA_JOB] [update weekly geosamples]', err);
        }

        await this._cache.set('geosamples:weekly-samples:offset', offset + chunkSize, 7 * TimeInSeconds.HOUR);

        return true;
    }

    public async findGeoSamplesFromDB({
        verticesLatLng,
        keywords,
        platformKeys,
        weeks,
        years,
        startDate,
        endDate,
    }: {
        /**
         * A list of vertices of the global grid, as returned by getContainingRectangle.
         * This function returns geosamples that match exactly one of these vertices.
         */
        verticesLatLng?: ILatlng[];
        keywords?: any[];
        platformKeys?: any[];
        weeks?: any[];
        years?: any[];
        startDate?: Date;
        endDate?: Date;
    }): Promise<IGeoSample[]> {
        weeks = weeks?.length
            ? weeks
            : startDate && endDate && getWeeksAndYearsFromDatesInterval(startDate, endDate, DateTimeIntervalType.WEEK_NUMBER);
        years = years?.length
            ? years
            : startDate && endDate && getWeeksAndYearsFromDatesInterval(startDate, endDate, DateTimeIntervalType.YEAR);

        const start = startDate && DateTime.fromJSDate(startDate).startOf('day').startOf('week').toJSDate();
        const end = endDate && DateTime.fromJSDate(endDate).endOf('day').endOf('week').toJSDate();
        return this._geoSamplesRepository.find({
            filter: {
                ...(verticesLatLng?.length ? { $or: verticesLatLng } : {}),
                keyword: keywords ? { $in: keywords } : { $nin: [] },
                platformKey: platformKeys ? { $in: platformKeys } : { $nin: [] },
                week: weeks ? { $in: weeks } : { $nin: [] },
                year: years ? { $in: years } : { $nin: [] },
                createdAt: start && end ? { $gte: start, $lte: end } : { $nin: [] },
            },
            options: {
                lean: true,
            },
        });
    }

    public async fetchGeoSampleFromPlatform({
        lat,
        lng,
        keyword,
        platformKey,
        region,
    }: Omit<SearchQuery, 'version'>): Promise<GmapsPlaceDto[]> {
        if (!lat || !lng || !keyword || !platformKey || !region) {
            throw new MalouError(MalouErrorCode.MISSING_PARAM);
        }
        switch (platformKey) {
            case GeoSamplePlatform.GMAPS:
                const res = await getRankingForKeywordAndLatLng(keyword, lat, lng, region);
                if (res.data.error_message) {
                    throw new MalouError(MalouErrorCode.KEYWORDS_GEOSAMPLE_ERROR, {
                        metadata: { rawError: String(res.data.error_message) },
                    });
                }

                const { results } = res.data;
                const validResults = results.filter((r) => googleMapsResultValidator.safeParse(r).success);
                if (validResults.length < results.length) {
                    logger.info('[fetchGeoSampleFromPlatform] invalid geosample results', { lat, lng, keyword, platformKey, results });
                }
                return validResults;
            default:
                throw new MalouError(MalouErrorCode.KEYWORDS_PLATFORM_NOT_AUTHORIZED);
        }
    }

    /**
     * Voir https://en.wikipedia.org/wiki/ISO_week_date
     */
    public getYearWeekIndex = (date: Date): number => {
        // Use weekYear instead of year to get the correct year based on the week number
        // For 2024-12-30, the weekNumber is 1, year is 2024 and weekYear is 2025
        const { weekNumber, weekYear } = DateTime.fromJSDate(date);
        // eslint-disable-next-line no-mixed-operators
        return weekYear * 100 + weekNumber;
    };

    private async _getWeeklyGmapsSearchQueries(): Promise<SearchQuery[]> {
        // Brand businesses don’t have latlng so we get only local businesses
        // here that all have an address with a region code
        //
        // Using the index { active: 1 }, {partialFilterExpression: { active: true }}
        const activeLocalBusinesses = await this._restaurantsRepository.find({
            filter: { active: true, latlng: { $ne: null } },
            projection: { _id: 1, latlng: 1, address: 1, gMapsApiVersion: 1 },
            options: { lean: true, sort: { gMapsApiVersion: 1 } },
        });

        const fetchSamplesMap: Record<string, { regionCode: CcTld; gMapsApiVersion: GMapsApiVersion }> = {};
        const thisWeekSamplesMap: Record<string, number> = {};
        // for each active restaurant, find its active keywords
        // filter out the geoSamples that were already fetched this week
        for (const activeLocalBusiness of activeLocalBusinesses) {
            if (!activeLocalBusiness.latlng || !activeLocalBusiness.address) {
                continue;
            }
            const {
                latlng: { lat, lng },
                address: { regionCode },
            } = activeLocalBusiness;
            if (!lat || !lng) {
                continue;
            }

            const { tl, tr, bl, br } = getContainingRectangle(lat, lng);
            const activeKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywords(
                activeLocalBusiness._id.toString()
            );
            const possibleSamplesToFetch = activeKeywords
                .map((activeKeyword) => {
                    const text = activeKeyword.keyword.text;
                    return [
                        `${text}__${tl.lat}__${tl.lng}`,
                        `${text}__${tr.lat}__${tr.lng}`,
                        `${text}__${bl.lat}__${bl.lng}`,
                        `${text}__${br.lat}__${br.lng}`,
                    ];
                })
                .flat(); // ['pizza__28.810__29.90','pizza__28.815__29.90', xxx]
            const ccTldRegionCode = ccTldByCountryCode[regionCode];
            if (!ccTldRegionCode) {
                continue;
            }
            possibleSamplesToFetch.forEach((sample) => {
                fetchSamplesMap[sample] = {
                    regionCode: ccTldRegionCode,
                    gMapsApiVersion: activeLocalBusiness.gMapsApiVersion ?? GMapsApiVersion.V2,
                };
            }); // {'pizza__28.810__29.90': {regionCode: 'fr', gMapsApiVersion: 'v2'} }
        }
        const today = DateTime.utc();

        // This query can match a few thousands of documents. It’s using the
        // index {week: 1, year: 1}.
        const cursor = this._geoSamplesRepository.model
            .find({ week: today.weekNumber, year: today.weekYear })
            .lean(true)
            .select({ keyword: 1, lat: 1, lng: 1 })
            .cursor();
        for await (const sample of cursor) {
            thisWeekSamplesMap[`${sample.keyword}__${sample.lat}__${sample.lng}`] = 1;
        }

        const finalList: SearchQuery[] = [];
        for (const [sampleKey, { regionCode, gMapsApiVersion }] of Object.entries(fetchSamplesMap)) {
            if (!thisWeekSamplesMap[sampleKey]) {
                const [keyword, lat, lng] = sampleKey.split('__');
                finalList.push({
                    keyword,
                    lat: +lat,
                    lng: +lng,
                    platformKey: GeoSamplePlatform.GMAPS,
                    region: regionCode,
                    version: gMapsApiVersion,
                });
            }
        }
        return finalList;
    }

    private async _processWeeklySamplesChunk(offset: number, queries: SearchQuery[]): Promise<void> {
        logger.info(`[AGENDA_JOB] [update weekly geosamples] - Processing samples`, {
            offset,
            samplesCount: queries.length,
        });

        const today = DateTime.utc();

        const geosamplesToInsert: GeoSampleWithoutIdAndDbDates[] = [];

        // Split queries into two lists
        const v1Queries: SearchQuery[] = [];
        const v2Queries: SearchQuery[] = [];

        for (const query of queries) {
            if (query.version === GMapsApiVersion.V1) {
                v1Queries.push(query);
            } else {
                v2Queries.push(query);
            }
        }

        logger.info('[AGENDA_JOB] [update weekly geosamples] [V1]', v1Queries);
        logger.info('[AGENDA_JOB] [update weekly geosamples] [V2]', v2Queries);

        const v1Promises = v1Queries.map(async ({ lat, lng, keyword, region, version }): Promise<void> => {
            const sampleToFetch = {
                lat,
                lng,
                keyword,
                platformKey: GeoSamplePlatform.GMAPS,
                week: today.weekNumber,
                year: today.weekYear,
                region,
                version,
            };

            try {
                const results = await this.fetchGeoSampleFromPlatform(sampleToFetch);

                const geoSampleDoc: Omit<IGeoSample, '_id' | 'createdAt' | 'updatedAt'> = {
                    ...sampleToFetch,
                    ranking: results,
                };

                if (results.length >= 1) {
                    const FAR_RESULT_IN_KM = 50;
                    const bestResultDistanceToSample = getDistanceFromLatLonInKm(
                        results[0].geometry.location.lat,
                        results[0].geometry.location.lng,
                        lat,
                        lng
                    );
                    if (bestResultDistanceToSample > FAR_RESULT_IN_KM) {
                        geoSampleDoc.errorData = 'results_too_far';
                        geoSampleDoc.error = true;
                    }
                }
                logger.info('[AGENDA_JOB] [update weekly geosamples] [API_SUCCESS_V1]', geoSampleDoc);
                geosamplesToInsert.push(geoSampleDoc);
            } catch (e) {
                logger.error('[AGENDA_JOB] [update weekly geosamples] [API_FAILED]', e);
            }
        });

        await Promise.all(v1Promises);

        const v2Results: { placeIds: string[]; lat: number; lng: number; keyword: string; region: string }[] = [];
        const v2Promises = v2Queries.map(async ({ lat, lng, keyword, region }): Promise<void> => {
            try {
                const result = await performNewTextSearch({
                    textQuery: keyword,
                    locationBias: {
                        circle: {
                            center: { latitude: lat, longitude: lng },
                            radius: +Config.geolocation.keywordRadiusSearchMeters,
                        },
                    },
                    regionCode: region,
                });

                logger.info('[GeoSampleService] [new gmaps search api] reply', result);

                if (result.success) {
                    v2Results.push({
                        lat,
                        lng,
                        keyword,
                        region,
                        placeIds: result.placeIds,
                    });
                }
            } catch (error: any) {
                logger.info('[GeoSampleService] [new gmaps search api] thrown error', error.stack);
            }
        });

        // We fetch all rankings for the restaurants that are using the new version
        await Promise.all(v2Promises);

        const allPlaceIds = v2Results.flatMap((r) => r.placeIds);

        await this._ensurePlaceDetailsAreSaved(allPlaceIds);

        const allPlaces = await this._mapsPlaceInfoRepository.find({
            filter: { platformKey: GeoSamplePlatform.GMAPS, placeId: { $in: allPlaceIds } },
            options: { lean: true },
        });

        const allPlacesByPlaceId = new Map(allPlaces.map((p) => [p.placeId, p]));

        for (const result of v2Results) {
            const places = result.placeIds.map((placeId): IMapsPlaceInfo => {
                const place = allPlacesByPlaceId.get(placeId);
                assert(place);
                return place;
            });
            const geoSample: GeoSampleWithoutIdAndDbDates = {
                lat: result.lat,
                lng: result.lng,
                keyword: result.keyword,
                platformKey: GeoSamplePlatform.GMAPS,
                week: today.weekNumber,
                year: today.weekYear,
                ranking: places.map((place) => ({
                    formatted_address: place.shortAddressText,
                    geometry: {
                        location: { lat: place.lat, lng: place.lng },
                        viewport: {
                            northeast: { lat: place.lat, lng: place.lng },
                            southwest: { lat: place.lat, lng: place.lng },
                        },
                    },
                    name: place.name,
                    place_id: place.placeId,
                })),
                region: result.region,
            };

            if (places.length >= 1) {
                const FAR_RESULT_IN_KM = 50;
                const bestResultDistanceToSample = getDistanceFromLatLonInKm(places[0].lat, places[0].lng, result.lat, result.lng);
                if (bestResultDistanceToSample > FAR_RESULT_IN_KM) {
                    geoSample.errorData = 'results_too_far';
                    geoSample.error = true;
                }
            }
            logger.info('[AGENDA_JOB] [update weekly geosamples] [API_SUCCESS_V2]', geoSample);

            geosamplesToInsert.push(geoSample);
        }

        for (const chunk of lodash.chunk(geosamplesToInsert, 10)) {
            await this.upsertGeosamples(chunk);
        }
    }

    /**
     * Upserts place information described within the given geosamples into the collection
     * `mapsplaceinfo`.
     *
     * If this function is called more than once with the same parameters, calls from the
     * second onwards will have no effect.
     */
    private async _insertMapsPlaceInfoFromGeosamples(geosamples: GeoSampleWithoutIdAndDbDates[]): Promise<void> {
        // to group geosamples by place IDs
        const placeInfoMap: Map<string, IMapsPlaceInfo> = new Map();
        for (const sample of geosamples) {
            for (const gmapsPlace of sample.ranking) {
                const lat = gmapsPlace.geometry?.location?.lat ?? undefined;
                const lng = gmapsPlace.geometry?.location?.lng ?? undefined;
                if (lat === undefined || lng === undefined) {
                    continue;
                }
                placeInfoMap.set(gmapsPlace.place_id, {
                    placeId: gmapsPlace.place_id,
                    lat,
                    lng,
                    platformKey: sample.platformKey,
                    name: gmapsPlace.name,
                    shortAddressText: gmapsPlace.formatted_address ?? gmapsPlace.vicinity ?? '',
                });
            }
        }

        await this._insertMapsPlaceInfo([...placeInfoMap.values()]);
    }

    /**
     * Inserts places into the collection mapsplaceinfo if necessary.
     *
     * Places we already know are not fetched from Google because the Places API can be
     * quite expensive.
     */
    private async _ensurePlaceDetailsAreSaved(placeIds: string[], regionCode?: string) {
        if (placeIds.length === 0) {
            return;
        }

        const knownPlaces = await this._mapsPlaceInfoRepository.find({
            filter: { platformKey: GeoSamplePlatform.GMAPS, placeId: { $in: placeIds } },
            options: { lean: true },
            projection: { placeId: 1 },
        });
        const unknownPlaceIds = placeIds.filter((id) => !knownPlaces.some((known) => known.placeId === id));
        const placesToSave: IMapsPlaceInfo[] = [];
        for (const placeId of lodash.uniq(unknownPlaceIds)) {
            logger.info('[GeoSampleService] [new gmaps search api] fetching place details', { placeId });
            try {
                const result = await performNewPlaceDetails({ regionCode, placeId });
                logger.info('[GeoSampleService] [new gmaps search api] place details reply', result);
                if (!result.success) {
                    continue;
                }
                placesToSave.push({
                    lat: result.place.location.latitude,
                    lng: result.place.location.longitude,
                    platformKey: GeoSamplePlatform.GMAPS,
                    name: result.place.displayName.text,
                    placeId,
                    shortAddressText: result.place.formattedAddress,
                });
            } catch (error: any) {
                logger.info('[GeoSampleService] [new gmaps search api] error thrown while fetching place details', error.stack);
                continue;
            }
        }
        logger.info('[GeoSampleService] [new gmaps search api] inserting', { placesToSave });
        // This will be enabled later.
        await this._insertMapsPlaceInfo(placesToSave);
    }

    private async _insertMapsPlaceInfo(placeInfos: IMapsPlaceInfo[]) {
        const docs = placeInfos.map((placeInfo): Omit<IMapsPlaceInfo, '_id'> => {
            const placeDoc = new MapsPlaceInfoModel(placeInfo);
            assert.strictEqual(placeDoc.validateSync(), undefined);
            const place: IMapsPlaceInfo = placeDoc.toObject();
            delete place._id;
            return place;
        });

        await this._mapsPlaceInfoRepository.bulkOperations({
            operations: docs.map((doc) => ({
                replaceOne: {
                    filter: { placeId: doc.placeId, platformKey: doc.platformKey },
                    replacement: doc,
                    upsert: true,
                },
            })),
            options: { ordered: false },
        });
    }

    /**
     * Returns restaurants that are located within one of the cells delimited by one of
     * the given vertices of the global grid, i.e. nearby.
     *
     * This function can return inactive restaurants. The caller should probably use the
     * 'active' field of the returned objects.
     */
    private async _findRestaurantsNearVertices(
        vertices: { lat: number; lng: number }[]
    ): Promise<Pick<IRestaurant, '_id' | 'placeId' | 'latlng' | 'active'>[]> {
        // A cell of the global grid includes its bottom left vertex:
        //
        //     > getContainingRectangle(48.88, 2.29)
        //     {
        //       tl: { lat: 48.885, lng: 2.29 },
        //       tr: { lat: 48.885, lng: 2.295 },
        //       br: { lat: 48.88, lng: 2.295 },
        //       bl: { lat: 48.88, lng: 2.29 }
        //     }
        //
        // That’s why we use `$gte` below, in the case a restaurant is located exactly on the
        // edge between two cells.
        return await this._restaurantsRepository.find({
            filter: {
                $or: vertices.map((vertex) => ({
                    'latlng.lat': { $gte: globalGridIncrement(vertex.lat, -1), $lt: globalGridIncrement(vertex.lat, 1) },
                    'latlng.lng': { $gte: globalGridIncrement(vertex.lng, -1), $lt: globalGridIncrement(vertex.lng, 1) },
                })),
            },
            projection: { _id: 1, placeId: 1, latlng: 1, active: 1 },
            options: { lean: true },
        });
    }

    private _isRestaurantNearVertex(vertex: { lat: number; lng: number }, restaurant: Pick<IRestaurant, 'latlng'>): boolean {
        if (!restaurant.latlng) {
            return false;
        }
        return (
            restaurant.latlng.lat >= globalGridIncrement(vertex.lat, -1) &&
            restaurant.latlng.lat < globalGridIncrement(vertex.lat, 1) &&
            restaurant.latlng.lng >= globalGridIncrement(vertex.lng, -1) &&
            restaurant.latlng.lng < globalGridIncrement(vertex.lng, 1)
        );
    }

    /**
     * The given geosamples are located exactly on vertices of the global grid. This function
     * returns place IDs that:
     *   - are located within one of the cells delimited by one of these vertices, i.e. nearby.
     *   - have enabled the keyword of one of these nearby geosamples.
     *
     * The keys of the returned map reference the given geosamples. The values of the returned
     * map is a list of place IDs. The map has one entry for each geosample.
     *
     * This function can return place IDs of inactive restaurants.
     */
    public async _findPlaceIdsAffectedByGeosamples(
        geosamples: GeoSampleWithoutIdAndDbDates[]
    ): Promise<Map<GeoSampleWithoutIdAndDbDates, Set<string>>> {
        if (geosamples.length === 0) {
            return new Map();
        }

        const verticesKeywords: { lat: number; lng: number; keywords: string[] }[] = [];
        for (const sample of geosamples) {
            let group = verticesKeywords.find((g) => g.lat === sample.lat && g.lng === sample.lng);
            if (!group) {
                group = { lat: sample.lat, lng: sample.lng, keywords: [] };
                verticesKeywords.push(group);
            }
            group.keywords.push(sample.keyword);
        }

        const allKeywords: Pick<IKeywordTemp, '_id' | 'text'>[] = await this._keywordsRepository.find({
            filter: {
                text: { $in: lodash.uniq(geosamples.map((g) => g.keyword)) },
            },
            projection: { _id: 1, text: 1 },
            options: { lean: true },
        });

        // This query ignores keywords so we can get a few more restaurants than expected.
        // We’ll have to filter it a second time later.
        //
        // This list can contain inactive restaurants.
        const allRestaurants = await this._findRestaurantsNearVertices(verticesKeywords);

        const allRestaurantKeywords: Pick<IRestaurantKeyword, 'restaurantId' | 'keywordId'>[] =
            await this._restaurantKeywordsRepository.find({
                filter: {
                    restaurantId: { $in: allRestaurants.map((r) => r._id) },
                    keywordId: { $in: allKeywords.map((k) => k._id) },
                    selected: true,
                },
                options: { lean: true },
                projection: { restaurantId: 1, keywordId: 1 },
            });

        // maps place IDs to keywords
        const selectedKeywordsByPlaceId = new Map<string, string[]>();
        for (const restaurant of allRestaurants) {
            const keywords: string[] = allRestaurantKeywords
                .filter((rk) => rk.restaurantId.toString() === restaurant._id.toString())
                .map((rk) => {
                    const keyword = allKeywords.find((k) => k._id.toString() === rk.keywordId.toString());
                    assert(keyword);
                    return keyword.text;
                });

            // many restaurants can share the same place IDs
            selectedKeywordsByPlaceId.set(restaurant.placeId, [...(selectedKeywordsByPlaceId.get(restaurant.placeId) ?? []), ...keywords]);
        }

        // maps geosamples to place IDs
        const map = new Map<GeoSampleWithoutIdAndDbDates, Set<string>>();
        for (const geosample of geosamples) {
            const affectedPlaceIds = new Set<string>();
            for (const restaurant of allRestaurants) {
                if (!this._isRestaurantNearVertex(geosample, restaurant) || restaurant.placeId == undefined) {
                    continue;
                }
                const selectedKeywords: string[] = selectedKeywordsByPlaceId.get(restaurant.placeId) ?? [];
                if (!selectedKeywords.includes(geosample.keyword)) {
                    continue;
                }
                affectedPlaceIds.add(restaurant.placeId);
            }
            map.set(geosample, affectedPlaceIds);
        }
        return map;
    }

    /**
     * Refreshes the collection `weeklysearchrankings` after new geosamples have been fetched.
     */
    private async _refreshWeeklySearchRankingsFromGeosamples(samples: GeoSampleWithoutIdAndDbDates[], hardFailure: boolean = false) {
        const begin = +new Date();
        try {
            const operations: AnyBulkWriteOperation[] = [];
            const samplesToPlaceIds = await this._findPlaceIdsAffectedByGeosamples(samples);
            for (const sample of samples) {
                const affectedPlaceIds = samplesToPlaceIds.get(sample);
                assert(affectedPlaceIds);
                for (const placeId of affectedPlaceIds) {
                    const op = this._refreshWeeklySearchRankingsFromGeosample(placeId, sample);
                    operations.push(op);
                }
            }
            const result = await this._weeklySearchRankingsRepository.bulkOperations({
                operations,
                options: { ordered: false },
            });
            weeklySearchRankingUpsertedCounter.add(result.modifiedCount + result.upsertedCount);
        } catch (error) {
            if (hardFailure) {
                throw error;
            }
            // We don’t want to affect SEO stats if there’s an issue with the new
            // weeklysearchrankings collection.
            // TODO: Remove this `catch` in the future.
            logger.error('[upsertGeosamples] issue with weeklysearchrankings', error);
        } finally {
            wsrBulkRefreshDuration.record((+new Date() - begin) / 1000);
        }
    }

    /**
     * Returns an operation that refreshes the collection `weeklysearchrankings` for a
     * restaurant when a new geosample has been fetched.
     *
     * The parameter `placeId` must match an existing restaurant that:
     *   - must be a Malou customer
     *   - must have the keyword of the geosample enabled among its list of tracked keywords
     *
     * The given sample must be located exactly at one of the vertices of the cell of the
     * given place ID.
     */
    private _refreshWeeklySearchRankingsFromGeosample(placeId: string, sample: GeoSampleWithoutIdAndDbDates): AnyBulkWriteOperation {
        const yearWeekIndex = sample.year * 100 + sample.week;

        const error = Boolean(!sample.ranking?.length && (sample.error || sample.errorData));
        const newVertexSearchResult = {
            lat: sample.lat,
            lng: sample.lng,
            resultPlaceIds: sample.ranking?.map((r) => r.place_id) ?? [],
            errorMessage: error ? (sample.errorData ?? '') : null,
        };

        // To help to understand the pipeline, a JavaScript version of each stage is written
        // in a comment above each stage, where the variable `doc` is the current document.
        const pipeline: UpdateAggregationStage[] = [
            // If the document is just created (it’s an upsert) it will not have a `vertices`
            // field.
            //
            // doc.vertices = Array.isArray(doc.vertices) ? doc.vertices : [];
            {
                $set: {
                    vertices: {
                        $cond: { if: { $isArray: '$vertices' }, then: '$vertices', else: [] },
                    },
                },
            },

            // Upsert the new search result in the list, potentially replacing a previous one
            // with the same coordinates.
            //
            // doc.vertices = [
            //     ...doc.vertices.filter(v =>
            //         !(
            //             v.lat === newVertexSearchResult.lat &&
            //             v.lng === newVertexSearchResult.lng
            //         ),
            //     ),
            //     newVertexSearchResult,
            // ];
            {
                $set: {
                    vertices: {
                        $concatArrays: [
                            {
                                $filter: {
                                    input: '$vertices',
                                    cond: {
                                        $not: {
                                            $and: [
                                                { $eq: ['$$this.lat', newVertexSearchResult.lat] },
                                                { $eq: ['$$this.lng', newVertexSearchResult.lng] },
                                            ],
                                        },
                                    },
                                },
                            },
                            { $literal: [newVertexSearchResult] },
                        ],
                    },
                },
            },

            // doc._placeIds = doc.vertices.reduce((v, acc) => lodash.union(acc, v.resultPlaceIds), []);
            // doc._errorMessages = doc.vertices.map(v => v.errorMessage).filter(m => typeof m === 'string');
            {
                $set: {
                    _placeIds: {
                        $reduce: {
                            input: '$vertices',
                            initialValue: [],
                            in: {
                                $setUnion: ['$$value', '$$this.resultPlaceIds'],
                            },
                        },
                    },

                    _errorMessages: {
                        $filter: {
                            input: {
                                $map: { input: '$vertices', in: '$$this.errorMessage' },
                            },
                            cond: { $eq: [{ $type: '$$this' }, 'string'] },
                        },
                    },
                },
            },

            // const error = (
            //     doc._errorMessages.length === doc.vertices.length &&
            //     doc._errorMessages.length > 0
            // );
            // doc.errorMessage = error ? doc._errorMessages[0] : null;
            {
                $set: {
                    errorMessage: {
                        $cond: {
                            if: {
                                $and: [
                                    { $eq: [{ $size: '$_errorMessages' }, { $size: '$vertices' }] },
                                    { $gt: [{ $size: '$_errorMessages' }, 0] },
                                ],
                            },
                            then: { $arrayElemAt: ['$_errorMessages', 0] },
                            else: null,
                        },
                    },
                },
            },

            // doc._placesWithRanks =
            //     doc._placeIds.map(
            //         placeId => ({
            //             placeId,
            //
            //             // The ranking of the place ID for each vertex of the cell.
            //             // 0 means that the place was the very first result, 19 means
            //             // that the place was ranked last.
            //             ranks: doc.vertices
            //                 .map(vertex => vertex.resultPlaceIds.indexOf(placeId))
            //                 .filter(i => i !== -1),
            //         }),
            //     );
            {
                $set: {
                    // A list with one entry for each place ID
                    _placesWithRanks: {
                        $map: {
                            input: '$_placeIds',
                            as: 'placeId',
                            in: {
                                placeId: '$$placeId',

                                ranks: {
                                    $filter: {
                                        input: {
                                            $map: {
                                                input: '$vertices',
                                                as: 'vertex',
                                                in: { $indexOfArray: ['$$vertex.resultPlaceIds', '$$placeId'] },
                                            },
                                        },
                                        cond: { $ne: ['$$this', -1] },
                                    },
                                },
                            },
                        },
                    },
                },
            },

            // const sum = list => list.reduce((sum, n) => sum + n, 0);
            //
            // doc._placesWithScore =
            //     doc._placesWithRanks.map(place => ({
            //         placeId: place.placeId,
            //         score: sum(place.ranks.map(index => RANK_SCORES[index])),
            //     }));
            {
                $set: {
                    _placesWithScore: {
                        $map: {
                            input: '$_placesWithRanks',
                            as: 'place',
                            in: {
                                placeId: '$$place.placeId',
                                score: {
                                    $sum: {
                                        $map: {
                                            input: '$$place.ranks',
                                            as: 'i',
                                            in: { $arrayElemAt: [RANK_SCORES, '$$i'] },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },

            // doc.overallRanking =
            //     lodash
            //        .sortBy(doc._placesWithScore, p => -p.score)
            //        .slice(0, 20)
            //        .map(p => p.placeId);
            {
                $set: {
                    overallRanking: {
                        $map: {
                            input: {
                                $firstN: {
                                    input: {
                                        $sortArray: { input: '$_placesWithScore', sortBy: { score: -1 } },
                                    },
                                    n: 20,
                                },
                            },
                            in: '$$this.placeId',
                        },
                    },
                },
            },

            // doc._overallRank = doc.overallRanking.indexOf(this.placeId); // can be -1
            // doc.overallOutOf = doc.overallRanking.length;
            {
                $set: {
                    _overallRank: { $indexOfArray: ['$overallRanking', '$placeId'] },
                    overallOutOf: { $size: '$overallRanking' },
                },
            },

            // We want the first result to be 1 instead of zero, and `null` means that the
            // place was not in the first 20 results:
            //
            // doc.overallRank = doc._overallRank === -1 ? null : (doc._overallRank + 1)
            {
                $set: {
                    overallRank: {
                        $cond: {
                            if: { $eq: ['$_overallRank', -1] },
                            then: null,
                            else: { $add: [1, '$_overallRank'] },
                        },
                    },
                },
            },

            // it’s convenient to comment this out for troubleshooting purposes
            { $unset: ['_placeIds', '_placesWithRanks', '_placesWithScore', '_overallRank', '_errorMessages'] },
        ];

        return {
            updateOne: {
                filter: {
                    keyword: sample.keyword,
                    placeId,
                    yearWeekIndex,
                    platformKey: GeoSamplePlatform.GMAPS,
                },
                update: pipeline,
                upsert: true,
            },
        };
    }

    private async _getGMapsApiVersion(placeId: string): Promise<GMapsApiVersion> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { placeId },
            projection: { gMapsApiVersion: 1 },
            options: { lean: true },
        });

        return restaurant?.gMapsApiVersion ?? GMapsApiVersion.V2;
    }
}
