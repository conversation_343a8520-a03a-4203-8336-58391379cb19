import { container } from 'tsyringe';

import { DbId, IRestaurant, IUser } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import ReportsRepository from ':modules/reports/reports.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

import { UserRestaurantsRepository } from './user-restaurants.repository';
import { UserRestaurantsUseCases } from './user-restaurants.use-cases';

const userRestaurantsRepository = container.resolve(UserRestaurantsRepository);
const userRestaurantsUseCases = container.resolve(UserRestaurantsUseCases);
const reportsRepository = container.resolve(ReportsRepository);

describe('user-restaurants.repository', () => {
    describe('updateUserRestaurants', () => {
        beforeAll(() => {
            registerRepositories(['RestaurantsRepository', 'UsersRepository', 'UserRestaurantsRepository']);
        });

        it('should remove userRestaurants when less userIds are set', async () => {
            const testCase = new TestCaseBuilder<'restaurants' | 'userRestaurants' | 'users'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().internalName('First').uniqueKey('12345').build(),
                                getDefaultRestaurant().internalName('Second').uniqueKey('67890').build(),
                                getDefaultRestaurant().internalName('Third').uniqueKey('49828').build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                                getDefaultUser().build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            const restaurants: IRestaurant[] = dependencies.restaurants as IRestaurant[];
                            const users: IUser[] = dependencies.users as IUser[];
                            return [
                                // First restaurant
                                getDefaultUserRestaurant().restaurantId(restaurants[0]._id).userId(users[0]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[0]._id).userId(users[1]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[0]._id).userId(users[2]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[0]._id).userId(users[3]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[0]._id).userId(users[4]._id).build(),
                                // Second restaurant
                                getDefaultUserRestaurant().restaurantId(restaurants[1]._id).userId(users[0]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[1]._id).userId(users[1]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[1]._id).userId(users[2]._id).build(),
                                // Third restaurant
                                getDefaultUserRestaurant().restaurantId(restaurants[2]._id).userId(users[3]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[2]._id).userId(users[4]._id).build(),
                                getDefaultUserRestaurant().restaurantId(restaurants[2]._id).userId(users[5]._id).build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();
            const { users, restaurants }: { users: IUser[]; restaurants: IRestaurant[] } = testCase.getSeededObjects() as any;
            const removeRestaurantForUserSpy = jest.spyOn(reportsRepository, 'removeRestaurantForUser');
            const addRestaurantForUserSpy = jest.spyOn(reportsRepository, 'addRestaurantForUser');

            const userRestaurants = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[0]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurants).toHaveLength(5);
            await userRestaurantsUseCases.updateUserRestaurants(restaurants[0]._id.toString(), [
                users[2]._id.toString(),
                users[3]._id.toString(),
            ]);
            const userRestaurantsAfterUpdate = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[0]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurantsAfterUpdate).toHaveLength(2);
            const userIdsAfterUpdate = userRestaurantsAfterUpdate.map((ur) => ur.userId.toString()).sort();
            const userIdsToCompare = [users[2]._id, users[3]._id].map((id) => (id as DbId).toString()).sort();
            expect(userIdsAfterUpdate).toEqual(userIdsToCompare);
            expect(removeRestaurantForUserSpy).toHaveBeenCalledTimes(3);
            expect(addRestaurantForUserSpy).toHaveBeenCalledTimes(0);
            removeRestaurantForUserSpy.mockClear();
            addRestaurantForUserSpy.mockClear();

            const userRestaurants2 = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[1]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurants2).toHaveLength(3);
            await userRestaurantsUseCases.updateUserRestaurants(restaurants[1]._id.toString(), [
                users[4]._id.toString(),
                users[7]._id.toString(),
                users[8]._id.toString(),
            ]);
            const userRestaurants2AfterUpdate = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[1]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurants2AfterUpdate).toHaveLength(3);
            const userIdsAfterUpdate2 = userRestaurants2AfterUpdate.map((ur) => ur.userId.toString()).sort();
            const userIdsToCompare2 = [users[4]._id, users[7]._id, users[8]._id].map((id) => (id as DbId).toString()).sort();
            expect(userIdsAfterUpdate2).toEqual(userIdsToCompare2);
            expect(removeRestaurantForUserSpy).toHaveBeenCalledTimes(3);
            expect(addRestaurantForUserSpy).toHaveBeenCalledTimes(2); // Because 2 have a first restaurant, and 1 already has a restaurant attached
            removeRestaurantForUserSpy.mockClear();
            addRestaurantForUserSpy.mockClear();

            const userRestaurants3 = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[2]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurants3).toHaveLength(3);
            await userRestaurantsUseCases.updateUserRestaurants(restaurants[2]._id.toString(), [
                users[0]._id.toString(),
                users[1]._id.toString(),
                users[4]._id.toString(),
                users[5]._id.toString(),
            ]);
            const userRestaurants3AfterUpdate = await userRestaurantsRepository.find({
                filter: { restaurantId: restaurants[2]._id as DbId },
                options: { lean: true },
            });
            expect(userRestaurants3AfterUpdate).toHaveLength(4);
            const userIdsAfterUpdate3 = userRestaurants3AfterUpdate.map((ur) => ur.userId.toString()).sort();
            const userIdsToCompare3 = [users[0]._id, users[1]._id, users[4]._id, users[5]._id].map((id) => (id as DbId).toString()).sort();
            expect(userIdsAfterUpdate3).toEqual(userIdsToCompare3);
            expect(removeRestaurantForUserSpy).toHaveBeenCalledTimes(1);
            expect(addRestaurantForUserSpy).toHaveBeenCalledTimes(2);
            removeRestaurantForUserSpy.mockClear();
            addRestaurantForUserSpy.mockClear();
        });
    });
});
