import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { omit } from 'lodash';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    deleteUserFromAllOwnedRestaurantsParamsValidator,
    deleteUsersByIdsBodyValidator,
    GetFrontChatUserEmailHashInputBodyDto,
    GetFrontChatUserEmailHashResponseDto,
    getFrontChatUserEmailHashValidator,
    getUserByIdParamsValidator,
    GetUsersByOrganizationIdParamsDto,
    getUsersByOrganizationIdParamsValidator,
    ImpersonateUserBodyDto,
    impersonateUserBodyValidator,
    ImpersonateUserResponseDto,
    OrganizationUserDto,
    SearchUsersQueryDto,
    searchUsersQueryValidator,
    updateUserLastVisitedRestaurantParamsValidator,
    updateUserProfileBodyValidator,
    UpdateUserProfileOutputBodyDto,
    updateUserRestaurantParamsValidator,
} from '@malou-io/package-dto';
import { toDbId, VirtualUser } from '@malou-io/package-models';
import { ApiResultV2, CaslAction, CaslSubject, EmailCategory, EmailType, MalouErrorCode, Role } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import MailingUseCases from ':modules/mailing/use-cases';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UserRestaurantsUseCases } from ':modules/user-restaurants/user-restaurants.use-cases';
import { DeleteUserFromAllOwnedRestaurantsUseCase } from ':modules/users/use-cases/delete-user-from-all-owned-restaurants/delete-user-from-all-owned-restaurants.use-case';
import { ImpersonateUserUseCase } from ':modules/users/use-cases/impersonate-user/impersonate-user.use-case';
import { SearchUsersUseCase } from ':modules/users/use-cases/search-users/search-users.use-case';

import { UsersDtoMapper } from './users.mapper.dto';
import { UsersRepository } from './users.repository';
import UsersUseCases from './users.use-cases';

@singleton()
export default class UsersController {
    constructor(
        private readonly _usersUseCases: UsersUseCases,
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _usersRepository: UsersRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _userRestaurantsUseCases: UserRestaurantsUseCases,
        private readonly _deleteUserFromAllOwnedRestaurantsUseCase: DeleteUserFromAllOwnedRestaurantsUseCase,
        private readonly _usersDtoMapper: UsersDtoMapper,
        private readonly _searchUsersUseCase: SearchUsersUseCase,
        private readonly _impersonateUserUseCase: ImpersonateUserUseCase
    ) {}

    async getUsersWithActualRestaurantsFields(req: Request, res: Response, next: NextFunction) {
        try {
            const filters = req.query;
            const users = await this._usersUseCases.getUsersWithActualRestaurantsField(filters);
            return res.json({ data: users });
        } catch (err) {
            next(err);
        }
    }

    async getUserRestaurants(req: RequestWithUser, res: Response, next: NextFunction) {
        try {
            const userId = req.user?._id?.toString();
            if (!userId) {
                res.status(400).end();
                return;
            }
            const usersRestaurants = await this._usersUseCases.getUserRestaurantsOfUserId(userId);
            return res.json({ data: usersRestaurants });
        } catch (err) {
            next(err);
        }
    }

    async createNewAccount(req: Request, res: Response, next: NextFunction) {
        try {
            const { user } = req.body;
            await this._usersUseCases.createAccount(omit(user, 'confirmPassword'));
            return res.json({ msg: 'User Created' });
        } catch (err) {
            next(err);
        }
    }

    async getUsers(req: Request<any, any, any, { fields: string; populate: string }>, res: Response, next: NextFunction) {
        try {
            const { fields = '', populate = '' } = req.query;
            const users = await this._usersRepository.find({
                filter: {},
                projection: fields.split(' ').reduce((acc, field) => ({ ...acc, [field]: 1 }), {}),
                options: { lean: true, populate: (populate.split(' ') as (keyof VirtualUser)[]).map((path) => ({ path })) },
            });
            return res.json({ msg: 'Success', data: users });
        } catch (err) {
            next(err);
        }
    }

    @Params(getUserByIdParamsValidator)
    async getUserById(req: Request, res: Response, next: NextFunction) {
        try {
            const { userId } = req.params;
            const user = await this._usersUseCases.getUserById(userId);
            if (!user) {
                throw new MalouError(MalouErrorCode.USER_NOT_FOUND, {
                    metadata: { userId },
                });
            }
            return res.json({ data: user });
        } catch (err) {
            next(err);
        }
    }

    async getUserByEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const { email } = req.params;
            const user = await this._usersUseCases.getUserByEmail(email);

            if (!user) {
                throw new MalouError(MalouErrorCode.USER_NOT_FOUND, {
                    metadata: { email },
                });
            }

            return res.json({ data: user });
        } catch (err) {
            next(err);
        }
    }

    async getUsersByRestaurantId(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility, 'Missing user restaurants ability');
            assert(req.userAbility, 'Missing user ability');
            const usersRestaurants = await this._userRestaurantsRepository.find({
                filter: { restaurantId: toDbId(restaurantId) },
                options: {
                    populate: [
                        { path: 'user', populate: [{ path: 'profilePicture' }] },
                        { path: 'restaurant', select: ['organizationId', 'name'], populate: [{ path: 'organization' }] },
                    ],
                    lean: true,
                },
            });

            const can1 = req.userRestaurantsAbility.can(CaslAction.READ, subject(CaslSubject.USER_RESTAURANT, usersRestaurants[0]));
            const can2 = req.userAbility.can(CaslAction.READ, subject(CaslSubject.USER_RESTAURANT, usersRestaurants[0])); // organization's owner can read
            if (!can1 && !can2) {
                throw new MalouError(MalouErrorCode.USER_CANNOT_SEE_RESTAURANTS);
            }

            assert(req.user, 'Missing user');
            const { _id: userId } = req.user;
            const user = await this._usersUseCases.getUserById(userId.toString());

            switch (user.role) {
                case Role.MALOU_BASIC:
                case Role.MALOU_GUEST:
                    return res.json({ data: usersRestaurants?.filter((ur) => ur.user?.role !== Role.ADMIN) });
                case Role.ADMIN:
                    return res.json({ data: usersRestaurants });
                default:
                    throw new MalouError(MalouErrorCode.USER_UNKNOWN_ROLE);
            }
        } catch (err) {
            next(err);
        }
    }

    @Params(getUsersByOrganizationIdParamsValidator)
    async getUsersByOrganizationId(
        req: Request<GetUsersByOrganizationIdParamsDto, never, never, never>,
        res: Response<ApiResultV2<OrganizationUserDto[]>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const users = await this._usersRepository.find({
                filter: { organizationIds: organizationId },
                projection: {
                    _id: 1,
                    name: 1,
                    lastname: 1,
                    email: 1,
                    role: 1,
                    defaultLanguage: 1,
                },
                options: { lean: true },
            });
            return res.json({ data: users.map((user) => this._usersDtoMapper.toOrganizationUsersDto(user)) });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateUserRestaurantParamsValidator)
    async updateUserRestaurant(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { user_restaurant_id: id } = req.params; // userRestaurant's id
            assert(req.userRestaurantsAbility, 'Missing user restaurants ability');
            assert(req.userAbility, 'Missing user ability');
            const userRestaurant = await this._userRestaurantsRepository.findOneOrFail({
                filter: { _id: id },
                options: { populate: [{ path: 'restaurant', populate: [{ path: 'organization' }] }], lean: true },
            });
            const bypassPermissionKeyList = ['displayPermissionsModal', 'displaySemanticAnalyses'];

            // We need to make sure that for each key the user has the ability to update (either with ur or user ability)
            for (const key of Object.keys(req.body)) {
                const able1 = req.userAbility.can('update', subject(CaslSubject.USER_RESTAURANT, userRestaurant), key);
                const able2 = req.userRestaurantsAbility.can('update', subject(CaslSubject.USER_RESTAURANT, userRestaurant), key);
                const bypass = bypassPermissionKeyList.includes(key);

                if (!(able1 || able2 || bypass)) {
                    throw new MalouError(MalouErrorCode.USER_CANNOT_UPDATE_USER_RESTAURANT, {
                        metadata: {
                            userId: userRestaurant?.userId,
                            restaurantId: userRestaurant?.restaurantId,
                            key,
                        },
                    });
                }
            }

            if (req.body?.caslRole) {
                const userRestaurants = await this._usersUseCases.getUserRestaurantsPopulatedWithUser({
                    restaurantId: userRestaurant.restaurantId,
                });
                const nonAdminUserRestaurants = userRestaurants.filter((ur) => ur.user.role !== Role.ADMIN);
                const willDowngradeLastOwner = this._usersUseCases.willDowngradeLastOwner(
                    nonAdminUserRestaurants,
                    userRestaurant._id,
                    req.body.caslRole
                );
                if (willDowngradeLastOwner) {
                    throw new MalouError(MalouErrorCode.USER_CANNOT_DOWNGRADE_LAST_OWNER);
                }
            }
            const updatedUserRestaurant = await this._usersUseCases.updateUserRestaurant({ _id: id }, req.body, {});
            if (req.body?.caslRole) {
                await this._usersUseCases.forceExpireAbilitySession(userRestaurant.userId);
            }
            return res.status(201).json({ msg: 'user restaurant updated', data: updatedUserRestaurant });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateUserLastVisitedRestaurantParamsValidator)
    async updateUserLastVisitedRestaurant(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            assert(req.user, 'Missing user');
            const { _id: userId } = req.user;

            await this._usersUseCases.updateUserLastVisitedRestaurantId({ userId, restaurantId });

            return res.json({ data: {} });
        } catch (err) {
            next(err);
        }
    }

    async createNewUser(req: RequestWithPermissions, res: Response, next: NextFunction) {
        let user;
        try {
            assert(req.user, 'Missing user');
            if (req.body?.role === Role.ADMIN && req.user.role !== Role.ADMIN)
                throw new MalouError(MalouErrorCode.USER_CANNOT_CREATE_ADMIN);
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(CaslAction.CREATE, subject(CaslSubject.USER, req.user));
            user = await this._usersUseCases.createUser(req.body);
            await this._usersUseCases.sendConfirmEmail(user);
            return res.json({
                msg: 'User created',
                data: user,
            });
        } catch (err) {
            try {
                if (user?._id) {
                    await this._usersUseCases.deleteUserById(user._id);
                }
            } catch (e) {
                logger.warn('[CREATING_USER_ERROR]', e);
            }
            next(err);
        }
    }

    async updateUserOrganizations(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { user_id: userId } = req.params;

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.USER, { _id: userId }),
                'organizationIds'
            );

            const updatedUser = await this._usersRepository.findOneAndUpdate({
                filter: { _id: toDbId(userId) },
                update: {
                    organizationIds: req.body.organizationIds,
                },
                options: {
                    populate: [
                        {
                            path: 'profilePicture',
                        },
                        {
                            path: 'restaurants',
                        },
                        {
                            path: 'organizations',
                        },
                    ],
                    lean: true,
                },
            });
            return res.json({
                msg: 'User updated',
                data: updatedUser,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(updateUserProfileBodyValidator)
    async updateUser(req: Request<any, any, UpdateUserProfileOutputBodyDto>, res: Response, next: NextFunction) {
        try {
            const { user_id: userId } = req.params;

            if (req.body.email) {
                req.body.email = req.body.email?.trim().toLowerCase();
            }

            const updatedUser = await this._usersRepository.findOneAndUpdate({
                filter: { _id: toDbId(userId) },
                update: req.body,
                options: {
                    populate: [
                        {
                            path: 'profilePicture',
                        },
                        {
                            path: 'restaurants',
                        },
                        {
                            path: 'organizations',
                        },
                    ],
                    lean: true,
                    new: true,
                },
            });

            return res.json({
                msg: 'User updated',
                data: updatedUser,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getFrontChatUserEmailHashValidator)
    async getFrontChatUserEmailHash(
        req: Request<any, any, GetFrontChatUserEmailHashInputBodyDto>,
        res: Response<ApiResultV2<GetFrontChatUserEmailHashResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { email, language } = req.body;
            const hash = this._usersUseCases.getFrontChatUserEmailHash(email, language);

            return res.json({
                data: { hash },
            });
        } catch (err) {
            next(err);
        }
    }

    async updateUserFromAdmin(req: Request, res: Response, next: NextFunction) {
        try {
            const { user_id: userId } = req.params;

            const updatedUser = await this._usersUseCases.updateUserFromAdmin(userId, req.body);

            return res.json({ msg: 'User updated', data: updatedUser });
        } catch (err) {
            next(err);
        }
    }

    async login(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, password } = req.body;
            const cleanEmail = email?.trim().toLowerCase();
            const user = await this._usersUseCases.getUserByEmailWithPasswordHash(cleanEmail);

            if (!user) {
                throw new MalouError(MalouErrorCode.USER_NOT_FOUND, {
                    metadata: {
                        email: cleanEmail,
                    },
                });
            }

            if (user.verified === false) {
                throw new MalouError(MalouErrorCode.USER_NOT_VERIFIED, {
                    metadata: {
                        email: cleanEmail,
                    },
                });
            }

            // check if password matches
            return user.comparePassword(password, async (err, isMatch) => {
                if (err) {
                    return next(err);
                }

                if (!isMatch) {
                    return next(
                        new MalouError(MalouErrorCode.USER_WRONG_PASSWORD, {
                            metadata: {
                                email: cleanEmail,
                            },
                        })
                    );
                }

                // if user is found and password is right create a token
                const token = await user.generateJwt();
                // return the information including token as JSON
                return res.json({ msg: 'Signed in', data: { token } });
            });
        } catch (err) {
            next(err);
        }
    }

    logout(req: Request, res: Response) {
        req.session.destroy((err) => {
            if (err) {
                return logger.info(err);
            }
            res.status(200).end();
        });
    }

    async sendResetPasswordEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const { email } = req.body;
            const user = await this._usersUseCases.getUserByEmailWithPasswordHash(email);
            if (!user) {
                // Return success even if user does not exist to avoid user enumeration
                return res.json({ msg: 'Email sent' });
            }

            // créer le token -> créer l'url -> envoyer l'email
            const token = this._usersUseCases.getTempUserToken({ password: user.password, _id: user._id, createdAt: user.createdAt });
            const resetUrl = this._usersUseCases.getPasswordResetUrl(user, token);

            this._mailingUseCases
                .sendEmail(EmailCategory.USER_NOTIF, EmailType.RESET_PASSWORD, { userId: user._id, url: resetUrl })
                .catch((error) => logger.warn('[SENDING_RESET_EMAIL] Error', { error, email, userId: user._id }));

            return res.json({ msg: 'Email sent' });
        } catch (err) {
            next(err);
        }
    }

    async getUserSettings(req: Request, res: Response, next: NextFunction) {
        try {
            const { user_id: userId } = req.params;
            const user = await this._usersUseCases.getUserSettings(userId);
            if (!user) {
                throw new MalouError(MalouErrorCode.USER_NOT_FOUND);
            }

            return res.json({ data: user?.settings?.receiveMessagesNotifications });
        } catch (err) {
            next(err);
        }
    }

    async confirmEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const { userId, token } = req.body;
            const user = await this._usersUseCases.getUserWithPasswordHash({ _id: userId });
            const secret = `${user.password}-${user.createdAt}`;
            const payload: any = jwt.verify(token, secret);
            let isUser = false;
            if (typeof payload === 'string') {
                isUser = payload === user.id;
            } else {
                isUser = payload.userId === user.id;
            }
            if (isUser) {
                await this._usersRepository.findOneAndUpdate({
                    filter: { _id: toDbId(userId) },
                    update: { verified: true },
                    options: {
                        populate: [
                            {
                                path: 'profilePicture',
                            },
                        ],
                    },
                });
            }
            return res.json({ msg: 'User confirmed', data: user });
        } catch (err) {
            next(err);
        }
    }

    async resetPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { userId, token } = req.params;
            const { password } = req.body;
            const user = await this._usersUseCases.getUserWithPasswordHash({ _id: userId });
            const secret = `${user.password}-${user.createdAt}`;
            const payload: any = jwt.verify(token, secret);
            let isUser = false;
            if (typeof payload === 'string') {
                isUser = payload === user.id;
            } else {
                isUser = payload.userId === user.id;
            }

            if (isUser) {
                // The password is hashed by a Mongoose hook
                await this._usersRepository.findOneAndUpdate({
                    filter: { _id: toDbId(userId) },
                    update: { password },
                    options: {
                        populate: [
                            {
                                path: 'profilePicture',
                            },
                        ],
                    },
                });
            }
            return res.json({ msg: 'Password updated' });
        } catch (err) {
            next(err);
        }
    }

    @Body(deleteUsersByIdsBodyValidator)
    async deleteUsers(req: Request, res: Response, next: NextFunction) {
        try {
            const { usersIds } = req.body;
            await this._usersUseCases.deleteManyUsers({ _id: usersIds });
            return res.json({ msg: 'Users deleted' });
        } catch (err) {
            next(err);
        }
    }

    @Query(searchUsersQueryValidator)
    async searchUsers(
        req: Request<any, any, any, SearchUsersQueryDto>,
        res: Response<ApiResultV2<Partial<OrganizationUserDto>[], { pagination: { total: number } }>>,
        next: NextFunction
    ) {
        try {
            const { text, fields, limit, offset } = req.query;

            const result = await this._searchUsersUseCase.execute({
                text,
                fields,
                limit,
                offset,
            });

            return res.json({
                data: result.data,
                metadata: { pagination: { total: result.total } },
            });
        } catch (err) {
            next(err);
        }
    }

    async deleteUserRestaurant(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { user_restaurant_id: userRestaurantId } = req.params;
            const userRestaurant = await this._userRestaurantsRepository.findOneOrFail({
                filter: { _id: userRestaurantId },
                options: { lean: true },
            });
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.DELETE,
                subject(CaslSubject.USER_RESTAURANT, userRestaurant)
            );
            await this._userRestaurantsUseCases.deleteUserRestaurant(userRestaurant);
            return res.json({ msg: 'User restaurant deleted' });
        } catch (err) {
            next(err);
        }
    }

    @Params(deleteUserFromAllOwnedRestaurantsParamsValidator)
    async deleteUserFromAllOwnedRestaurants(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { userId } = req.params;
            assert(req.user, 'Missing user');
            assert(req.userRestaurantsAbility, 'Missing user restaurants ability');
            const ownerId = req.user._id;
            await this._deleteUserFromAllOwnedRestaurantsUseCase.execute({
                userId,
                ownerId: ownerId.toString(),
                ownerRestaurantsAbility: req.userRestaurantsAbility,
            });
            return res.json({ msg: 'User restaurant deleted' });
        } catch (err) {
            next(err);
        }
    }

    async unsubscribeUser(req: Request, res: Response, next: NextFunction) {
        const { user_id: userId } = req.params;
        const { field } = req.query;
        try {
            const update = {};
            let fieldToUpdate;
            switch (field) {
                case 'receiveFeedbacks':
                    fieldToUpdate = 'settings.receiveFeedbacks';
                    update[fieldToUpdate] = false;
                    break;
                case 'receiveMessagesNotifications':
                    fieldToUpdate = 'settings.receiveMessagesNotifications.active';
                    update[fieldToUpdate] = false;
                    break;
                default:
            }

            const userUpdated = await this._usersRepository.upsert({
                filter: { _id: toDbId(userId) },
                update: { [fieldToUpdate]: update[fieldToUpdate] },
                options: {
                    populate: [
                        {
                            path: 'profilePicture',
                        },
                    ],
                },
            });

            if (!userUpdated) {
                throw new MalouError(MalouErrorCode.INVALID_DATA, {
                    metadata: {
                        userId,
                        update,
                    },
                });
            }

            res.redirect(`${process.env.BASE_URL}/users/${userId}?unsubscribed=true&field=${field}`);
        } catch (err) {
            res.redirect(`${process.env.BASE_URL}/users/${userId}?unsubscribed=false&field=${field}`);
            next(err);
        }
    }

    @Body(impersonateUserBodyValidator)
    async impersonateUser(
        req: Request<never, never, ImpersonateUserBodyDto>,
        res: Response<ApiResultV2<ImpersonateUserResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { userId: targetUserId } = req.body;
            const adminUserId = req.user._id;

            const { token } = await this._impersonateUserUseCase.execute(adminUserId, targetUserId);

            return res.json({
                data: { token },
            });
        } catch (err) {
            next(err);
        }
    }
}
