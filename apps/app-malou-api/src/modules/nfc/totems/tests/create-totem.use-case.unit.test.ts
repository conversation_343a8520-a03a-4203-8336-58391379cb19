import { container } from 'tsyringe';

import { CreateTotemBodyDto, NfcDto } from '@malou-io/package-dto';
import { generateDbId, NfcsPlatformKey, NfcType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilder } from ':helpers/tests/testing-utils';
import { CreateTotemUseCase } from ':modules/nfc/totems/use-cases/create-totem/create-totem.use-case';

describe('CreateTotemUseCase', () => {
    beforeAll(() => {
        registerRepositories(['TotemsRepository']);
    });

    describe('execute', () => {
        it('should create and return the totem', async () => {
            const createTotemUseCase = container.resolve(CreateTotemUseCase);

            const totemToCreate: CreateTotemBodyDto = {
                restaurantId: generateDbId().toString(),
                notes: 'notes',
                platformKey: NfcsPlatformKey.GMB,
                redirectionLink: 'http://google.com',
                active: true,
                starsRedirected: [1, 2, 3, 5],
                chipName: '1234',
                name: '1234',
            };

            const testCase = new TestCaseBuilder({
                seeds: {},
                expectedResult(): NfcDto {
                    return {
                        id: expect.any(String),
                        restaurantId: totemToCreate.restaurantId,
                        notes: totemToCreate.notes,
                        platformKey: totemToCreate.platformKey,
                        redirectionLink: totemToCreate.redirectionLink,
                        active: totemToCreate.active,
                        starsRedirected: totemToCreate.starsRedirected,
                        chipName: totemToCreate.chipName,
                        name: totemToCreate.name,
                        type: NfcType.TOTEM,
                        createdAt: expect.any(String),
                        updatedAt: expect.any(String),
                    };
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const result = await createTotemUseCase.execute(totemToCreate);
            expect(result).toEqual(expectedResult);
        });
    });
});
