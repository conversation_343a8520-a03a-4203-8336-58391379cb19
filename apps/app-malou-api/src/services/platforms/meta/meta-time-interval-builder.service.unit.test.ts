import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { TestCaseBuilder } from ':helpers/tests/testing-utils';

import { MetaTimeIntervalBuilderService } from './meta-time-interval-builder.service';

describe('MetaTimeIntervalBuilderService ', () => {
    let metaTimeIntervalBuilderService: MetaTimeIntervalBuilderService;
    const now: DateTime = DateTime.now();

    beforeAll(() => {
        metaTimeIntervalBuilderService = container.resolve(MetaTimeIntervalBuilderService);
    });

    describe('buildFacebookTimeIntervals ', () => {
        it('should split time interval on 2', async () => {
            const testCase = new TestCaseBuilder({
                seeds: {},
                expectedResult() {
                    return [
                        {
                            startDate: now.minus({ days: 92 * 2 }).toISODate(),
                            endDate: now.minus({ days: 92 }).toISODate(),
                        },
                        {
                            startDate: now.minus({ days: 92 }).toISODate(),
                            endDate: now.toISODate(),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const splittedTimeInterval = await metaTimeIntervalBuilderService.buildTimeInterval(PlatformKey.FACEBOOK, {
                startDate: now.minus({ days: 92 * 2 }),
                endDate: now,
            });

            expect(splittedTimeInterval.length).toBe(2);
            expect(
                splittedTimeInterval.map((value) => ({
                    startDate: value.startDate.toISODate(),
                    endDate: value.endDate.toISODate(),
                }))
            ).toEqual(expectedResult);
        });

        it('should not split time interval', async () => {
            const testCase = new TestCaseBuilder({
                seeds: {},
                expectedResult() {
                    return [
                        {
                            startDate: now.minus({ days: 91 }).toISODate(),
                            endDate: now.toISODate(),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const splittedTimeInterval = await metaTimeIntervalBuilderService.buildTimeInterval(PlatformKey.FACEBOOK, {
                startDate: now.minus({ days: 91 }),
                endDate: now,
            });

            expect(splittedTimeInterval.length).toBe(1);
            expect(
                splittedTimeInterval.map((value) => ({
                    startDate: value.startDate.toISODate(),
                    endDate: value.endDate.toISODate(),
                }))
            ).toEqual(expectedResult);
        });
    });

    describe('buildInstagramTimeIntervals ', () => {
        it('should split time interval on 2', async () => {
            const testCase = new TestCaseBuilder({
                seeds: {},
                expectedResult() {
                    return [
                        {
                            startDate: now.minus({ days: 29 * 2 }).toISODate(),
                            endDate: now.minus({ days: 29 }).toISODate(),
                        },
                        {
                            startDate: now.minus({ days: 29 }).toISODate(),
                            endDate: now.toISODate(),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const splittedTimeInterval = await metaTimeIntervalBuilderService.buildTimeInterval(PlatformKey.INSTAGRAM, {
                startDate: now.minus({ days: 29 * 2 }),
                endDate: now,
            });

            expect(splittedTimeInterval.length).toBe(2);
            expect(
                splittedTimeInterval.map((value) => ({
                    startDate: value.startDate.toISODate(),
                    endDate: value.endDate.toISODate(),
                }))
            ).toEqual(expectedResult);
        });

        it('should not split time interval', async () => {
            const testCase = new TestCaseBuilder({
                seeds: {},
                expectedResult() {
                    return [
                        {
                            startDate: now.minus({ days: 28 }).toISODate(),
                            endDate: now.toISODate(),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const splittedTimeInterval = await metaTimeIntervalBuilderService.buildTimeInterval(PlatformKey.INSTAGRAM, {
                startDate: now.minus({ days: 28 }),
                endDate: now,
            });

            expect(splittedTimeInterval.length).toBe(1);
            expect(
                splittedTimeInterval.map((value) => ({
                    startDate: value.startDate.toISODate(),
                    endDate: value.endDate.toISODate(),
                }))
            ).toEqual(expectedResult);
        });
    });
});
