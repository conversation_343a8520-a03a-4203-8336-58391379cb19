import 'reflect-metadata';

import ':env';

import axios from 'axios';
import assert from 'node:assert';
import probe from 'probe-image-size';
import prompts from 'prompts';
import sharp from 'sharp';
import { autoInjectable, container } from 'tsyringe';

import { IMedia, MediaModel } from '@malou-io/package-models';
import { MimeType } from '@malou-io/package-utils';

import { randomString } from ':helpers/utils';
import { MediasRepository } from ':modules/media/medias.repository';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

const RATIO_TOLERANCE = 0.01;

/**
 * If the media had exif orientation data, the dimensions.original could be inverted.
 * This was the case for old media and new medias duplicated from these old medias.
 */
@autoInjectable()
class FixMediaWithExifOrInvertedDimensionsTask {
    constructor(
        private readonly _mediaRepository: MediasRepository,
        private readonly _awsS3DistantStorageService: AwsS3DistantStorageService
    ) {}

    async execute() {
        const filter = { createdAt: { $gte: new Date('2025-01-01') }, type: 'photo', isV2: { $ne: true } };

        const count = await this._mediaRepository.countDocuments({ filter });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to check ${count} documents`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const mediasCursor = await MediaModel.find(filter, '', { lean: true }).cursor();

        let i = 0;
        for await (const media of mediasCursor) {
            i++;
            if (i % 500 === 0) {
                console.log('i', i);
            }
            try {
                if (!media.urls.original) {
                    console.log('not enough data', media._id.toString());
                    continue;
                }

                const meta = await probe(media.urls.original);
                assert(meta.width);
                assert(meta.height);
                if (meta.orientation !== undefined && meta.orientation !== 1) {
                    const buffer = await axios.get(media.urls.original, { responseType: 'arraybuffer' });
                    await this._processMediaWithExifOrientation(media, buffer.data);
                } else {
                    await this._processMediaWithoutExifOrientation(media, { width: meta.width, height: meta.height });
                }
            } catch (error) {
                console.log('error', media._id.toString(), error);
            }
        }
    }

    private async _processMediaWithExifOrientation(media: IMedia, buffer: Buffer): Promise<void> {
        console.log('Fixing media WITH exif', media._id.toString());
        return;
        const bufferWithoutExif = await sharp(buffer).rotate().jpeg().toBuffer({ resolveWithObject: true });
        const newOriginalDimensions = {
            width: bufferWithoutExif.info.width,
            height: bufferWithoutExif.info.height,
        };
        if (!this._isParamDimensionsMatchWithIgFitAndSmall(media, newOriginalDimensions)) {
            console.log('need manual fix (a)', media._id.toString());
            return;
        }
        const newOriginalS3Key = this._computeNewS3KeyInSameFolderAsUrlInParam(media.urls.original);
        await this._awsS3DistantStorageService.saveFromBuffer(newOriginalS3Key, bufferWithoutExif.data, {
            contentType: MimeType.IMAGE_JPEG,
        });
        const newOriginalUrl = await this._awsS3DistantStorageService.getPublicAccessibleUrl(newOriginalS3Key);

        await this._mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { 'urls.original': newOriginalUrl, 'dimensions.original': newOriginalDimensions },
        });

        await this._fixResizeMetadata(media, newOriginalDimensions);
    }

    private async _processMediaWithoutExifOrientation(media: IMedia, meta: { width: number; height: number }): Promise<void> {
        if (media.dimensions?.original && this._isParamDimensionsMatchWithIgFitAndSmall(media, media.dimensions.original)) {
            return;
        }

        console.log('Fixing media WITHOUT exif', media._id.toString());
        return;

        const newOriginalDimensions = {
            width: meta.width,
            height: meta.height,
        };

        if (!this._isParamDimensionsMatchWithIgFitAndSmall(media, newOriginalDimensions)) {
            console.log('need manual fix (b)', media._id.toString());
            return;
        }

        await this._mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { 'dimensions.original': newOriginalDimensions },
        });

        await this._fixResizeMetadata(media, newOriginalDimensions);
    }

    private _computeNewS3KeyInSameFolderAsUrlInParam(originalUrl: string): string {
        const url = new URL(originalUrl);
        const path = url.pathname.split('/').slice(0, -1); // removes the file name
        const uuid = randomString(6);
        path.push(`original-${uuid}.jpeg`); // re-add the modified file name
        return path.join('/');
    }

    private async _fixResizeMetadata(media: IMedia, newOriginalDimensions: { width: number; height: number }): Promise<void> {
        if (!media.resizeMetadata) {
            return;
        }

        const resizeMetadataDimensions = { width: media.resizeMetadata.width, height: media.resizeMetadata.height };

        if (this._doesDimensionsMatch(resizeMetadataDimensions, newOriginalDimensions)) {
            return;
        }

        const newResizeMetadata: IMedia['resizeMetadata'] = {
            aspectRatio: 1 / media.resizeMetadata.aspectRatio,
            width: media.resizeMetadata.height,
            height: media.resizeMetadata.width,
            cropPosition: {
                left: media.resizeMetadata.cropPosition.top,
                top: media.resizeMetadata.cropPosition.left,
            },
        };

        await this._mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { 'dimensions.original': newOriginalDimensions, resizeMetadata: newResizeMetadata },
        });
    }

    private _doesDimensionsMatch(a: { width: number; height: number }, b: { width: number; height: number }): boolean {
        const ratioA = a.width / a.height;
        const ratioB = b.width / b.height;
        return Math.abs(ratioA - ratioB) < RATIO_TOLERANCE;
    }

    private _isParamDimensionsMatchWithIgFitAndSmall(media: IMedia, dimensions: { width: number; height: number }): boolean {
        if (!media.dimensions?.igFit && !media.dimensions?.small) {
            return true;
        }

        if (media.dimensions?.igFit) {
            if (!this._doesDimensionsMatch(dimensions, media.dimensions.igFit)) {
                return false;
            }
        }

        if (media.dimensions?.small) {
            if (!this._doesDimensionsMatch(dimensions, media.dimensions.small)) {
                return false;
            }
        }

        return true;
    }
}

const task = container.resolve(FixMediaWithExifOrInvertedDimensionsTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
