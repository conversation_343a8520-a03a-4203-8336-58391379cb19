import ':env';

import { AnyBulkWriteOperation } from 'mongodb';
import prompts from 'prompts';

import { ID, IScan, ITotem, ScanModel, toDbId, TotemModel } from '@malou-io/package-models';
import { isNotNil, NfcType, PlatformKey } from '@malou-io/package-utils';

import ':plugins/db';

type MixedScan = IScan & {
    active: boolean;
    restaurantId: ID;
    platformKey: PlatformKey;
    chipName: string;
    redirectionLink: string;
};

async function main() {
    const filter = {
        nfcId: { $exists: false },
        chipName: { $exists: true },
        restaurantId: { $exists: true },
        active: { $exists: true },
        redirectionLink: { $exists: true },
        platformKey: { $exists: true },
        createdAt: { $exists: true },
        updatedAt: { $exists: true },
    };

    const count = await ScanModel.countDocuments(filter);

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to replace ${count} scans`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    const scansCursor = ScanModel.find<MixedScan>(filter, {}, { lean: true }).cursor();

    let processedCount = 0;
    let noNfcCount = 0;
    await scansCursor.eachAsync(
        async (scans: MixedScan[]) => {
            const nfcs: ITotem[] = await TotemModel.find({ chipName: { $in: scans.map((e) => e.chipName) } }, {}, { lean: true });
            const bulkOperations = scans.map((scan: MixedScan): AnyBulkWriteOperation<IScan> | null => {
                const nfc = nfcs.find((e) => e.chipName === scan.chipName);
                if (!nfc) {
                    noNfcCount++;
                    console.log('nfc not found for scan :>> ', scan._id);
                    return null;
                }
                const nfcSnapShot: IScan['nfcSnapshot'] = {
                    _id: nfc._id,
                    chipName: scan.chipName,
                    restaurantId: toDbId(scan.restaurantId),
                    active: scan.active,
                    platformKey: scan.platformKey,
                    redirectionLink: scan.redirectionLink,
                    type: NfcType.TOTEM,
                    createdAt: nfc.createdAt,
                    updatedAt: nfc.updatedAt,
                    starsRedirected: [0],
                };
                return {
                    replaceOne: {
                        filter: {
                            _id: scan._id,
                        },
                        replacement: {
                            nfcId: nfc._id,
                            scannedAt: scan.createdAt,
                            nfcSnapshot: nfcSnapShot,
                            redirectedAt: scan.createdAt,
                            createdAt: scan.createdAt,
                            updatedAt: new Date(),
                        },
                    },
                };
            });
            const bulkOperationsFiltered = bulkOperations.filter(isNotNil);
            await ScanModel.bulkWrite(bulkOperationsFiltered, { ordered: false });
            processedCount += scans.length;
            console.log('processedCount :>> ', processedCount);
        },
        { batchSize: 100, continueOnError: true }
    );

    console.log('processedCount :>> ', processedCount);
    console.log('noNfcCount :>> ', noNfcCount);
}

main()
    .then(() => process.exit(0))
    .catch((e) => {
        console.error('e :>> ', e);
        process.exit(1);
    });
